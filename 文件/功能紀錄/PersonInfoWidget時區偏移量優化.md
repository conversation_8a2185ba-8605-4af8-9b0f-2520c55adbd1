# PersonInfoWidget 時區偏移量顯示優化

## 📋 **修改概述**

優化 `PersonInfoWidget` 中的 `_buildTimezoneInfoRow` 方法，使其使用 `JulianDateUtils.calculateZonedDateTimeOffset` 來計算精確的時區偏移量並顯示。

## 🎯 **修改目標**

1. **精確計算時區偏移量** - 使用 `JulianDateUtils.calculateZonedDateTimeOffset` 方法
2. **顯示完整時區信息** - 包含時區名稱和 UTC 偏移量
3. **處理夏令時** - 自動考慮夏令時影響
4. **改善用戶體驗** - 提供更準確的時區信息

## 🔧 **技術實現**

### **修改的文件**
- `lib/shared/widgets/person_info_widget.dart`

### **新增導入**
```dart
import '../../core/utils/logger_utils.dart';
```

### **主要修改**

#### **1. 重構 `_buildTimezoneInfoRow` 方法**

**修改前**：
- 只顯示時區名稱
- 使用簡化的經度除以15的估算方法
- 沒有考慮夏令時影響

**修改後**：
- 使用 `_getTimezoneInfo` 輔助方法獲取完整時區信息
- 顯示時區名稱和精確的 UTC 偏移量
- 格式：`Asia/Taipei (UTC+8)` 或 `America/New_York (UTC-4.5)`

#### **2. 新增 `_getTimezoneInfo` 輔助方法**

```dart
Future<Map<String, dynamic>> _getTimezoneInfo(BirthData birthData) async {
  try {
    // 獲取時區名稱
    final timezoneName = await EnhancedTimezoneService().getTimeZoneFromLatLng(
      birthData.latitude,
      birthData.longitude,
    );
    
    // 使用 JulianDateUtils 計算精確的時區偏移量
    final offset = await JulianDateUtils.calculateZonedDateTimeOffset(
      birthData.latitude,
      birthData.longitude,
      birthData.dateTime.year,
      birthData.dateTime.month,
      birthData.dateTime.day,
      birthData.dateTime.hour,
      birthData.dateTime.minute,
    );
    
    return {
      'name': timezoneName,
      'offset': offset,
    };
  } catch (e) {
    logger.e('獲取時區信息時出錯: $e');
    return {};
  }
}
```

## ✨ **功能特點**

### **1. 精確的時區偏移量計算**
- 使用 `JulianDateUtils.calculateZonedDateTimeOffset` 方法
- 考慮夏令時影響
- 支援小數點偏移量（如 UTC+5.5）

### **2. 智能顯示格式**
- 整數偏移量：`UTC+8`
- 小數偏移量：`UTC+5.5`
- 負偏移量：`UTC-5`
- 完整格式：`Asia/Taipei (UTC+8)`

### **3. 錯誤處理機制**
- 載入狀態顯示：`載入中...`
- 錯誤時備用方案：使用經度估算 `UTC+8 (估算)`
- 異常處理：記錄錯誤日誌

### **4. 異步處理**
- 使用 `FutureBuilder` 處理異步計算
- 避免阻塞 UI 渲染
- 提供適當的載入狀態

## 🔍 **使用範例**

### **顯示效果**
```
時區: Asia/Taipei (UTC+8)
時區: America/New_York (UTC-5)
時區: Asia/Kolkata (UTC+5.5)
時區: UTC+8 (估算)  // 錯誤時的備用顯示
```

## 📝 **注意事項**

1. **依賴關係** - 需要確保 `JulianDateUtils` 和 `EnhancedTimezoneService` 正常運作
2. **性能考量** - 時區計算是異步操作，避免重複計算
3. **錯誤處理** - 提供適當的備用方案和錯誤提示
4. **日誌記錄** - 記錄錯誤信息便於調試

## ✅ **測試結果**

- ✅ 代碼分析通過
- ✅ 編譯成功
- ✅ 沒有語法錯誤
- ✅ 保持現有 UI 設計風格

## 🚀 **後續優化 - 快取機制**

### **問題**
`_buildTimezoneInfoRow` 在上下滾動時會一直重新計算，造成性能問題。

### **解決方案**
1. **改為 StatefulWidget** - 支援狀態管理和快取
2. **添加時區快取** - 使用 `Map<String, Map<String, dynamic>> _timezoneCache`
3. **快取 key 生成** - 基於經緯度和日期時間生成唯一 key
4. **避免重複計算** - 檢查快取，存在則直接使用

### **快取機制實現**

```dart
class _PersonInfoWidgetState extends State<PersonInfoWidget> {
  // 時區信息快取
  final Map<String, Map<String, dynamic>> _timezoneCache = {};

  /// 生成時區快取 key
  String _generateTimezoneKey(BirthData birthData) {
    return '${birthData.latitude.toStringAsFixed(4)}_${birthData.longitude.toStringAsFixed(4)}_${birthData.dateTime.year}_${birthData.dateTime.month}_${birthData.dateTime.day}_${birthData.dateTime.hour}_${birthData.dateTime.minute}';
  }

  /// 構建時區資訊行
  Widget _buildTimezoneInfoRow(BirthData birthData, {Color? color}) {
    // 生成快取 key
    final cacheKey = _generateTimezoneKey(birthData);

    // 檢查快取
    if (_timezoneCache.containsKey(cacheKey)) {
      return _buildTimezoneInfoWidget(_timezoneCache[cacheKey]!, color);
    }

    return FutureBuilder<Map<String, dynamic>>(
      future: _getTimezoneInfo(birthData),
      builder: (context, snapshot) {
        // ... 處理結果並儲存到快取
        _timezoneCache[cacheKey] = timezoneData;
        return _buildTimezoneInfoWidget(timezoneData, color);
      },
    );
  }
}
```

## ✅ **測試結果**

- ✅ **代碼分析通過** - 沒有語法錯誤
- ✅ **編譯成功** - APK 建置完成
- ✅ **快取機制** - 避免重複計算
- ✅ **性能優化** - 滾動時不會重新計算

## 🚀 **後續建議**

1. **測試不同時區** - 驗證各種時區的偏移量計算是否正確
2. **測試夏令時** - 確認夏令時期間的偏移量計算
3. **測試快取效果** - 確認滾動時不會重複計算
4. **UI 測試** - 確認顯示效果符合設計要求
