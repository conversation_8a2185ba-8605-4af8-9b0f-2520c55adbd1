import '../../../core/utils/logger_utils.dart';
import '../../models/astrology/chart_data.dart';
import 'ai_api_service.dart';

/// 星盤解讀服務，提供各種類型的星盤解讀功能
class ChartInterpretationService {
  /// 組裝提示詞
  static Future<String> getPrompt(
      ChartData chartData, String interpretationTitle, String subtitle,
      {String? keyPoint, String? userMode, String? analysisMethod}) async {
    final buffer = StringBuffer();

    // 添加解讀模式和分析方式信息
    if (userMode != null) {
      buffer.writeln("解讀模式：");
      if (userMode == 'starlight') {
        buffer.writeln("初心者模式 - 請使用簡化的占星術語，重點解釋基本概念，提供通俗易懂的分析，避免過於複雜的技術細節。");
      } else {
        buffer.writeln("占星師模式 - 請使用專業的占星術語，提供深度的技術分析。");
      }
      buffer.writeln();
    }

    if (analysisMethod != null) {
      buffer.writeln("分析方式：");
      if (analysisMethod == 'classical') {
        buffer.writeln("古典占星 - 使用整宮制");
      } else {
        buffer.writeln("現代占星 - 使用普拉西德制");
      }
      buffer.writeln();
    }

    if (interpretationTitle.isNotEmpty) {
      buffer
        ..writeln("解讀要求：")
        ..writeln(interpretationTitle);
    }
    if (subtitle.isNotEmpty) {
      buffer
        ..writeln()
        ..writeln("解讀主題：")
        ..writeln(subtitle);
    }
    if (keyPoint != null && keyPoint.isNotEmpty) {
      buffer
        ..writeln()
        ..writeln("分析重點：")
        ..writeln(keyPoint);
    }

    if (chartData.primaryPerson.isTimeUncertain ||
        (chartData.secondaryPerson != null &&
            chartData.secondaryPerson!.isTimeUncertain)) {
      buffer.writeln();
      buffer.writeln("出生時間不確定，重點關注分析太陽星座和主要行星位置");
      buffer.writeln("不要分析下列因素：");
      buffer.writeln("上升中天下降天底四軸");
      buffer.writeln("宮位分析");
      buffer.writeln("月亮星座");
      buffer.writeln("行星宮位位置");
      buffer.writeln("相位");
    }

    String prompt = buffer.toString();

    return prompt;
  }

  /// 獲取星盤解讀
  static Future<AIApiResponse> getChartInterpretation(
      ChartData chartData, String interpretationTitle, String subtitle,
      {String? keyPoint}) async {
    String prompt = await getPrompt(chartData, interpretationTitle, subtitle,
        keyPoint: keyPoint);

    return await AIApiService.getChartInterpretation(
      chartData: chartData,
      prompt: prompt,
    );
  }

  /// 獲取自定義解讀
  static Future<AIApiResponse> getCustomInterpretation({
    required ChartData chartData,
    required String customPrompt,
  }) async {
    logger.i("獲取自定義星盤解讀");
    logger.d("自定義提示詞：$customPrompt");

    return await AIApiService.getChartInterpretation(
      chartData: chartData,
      prompt: customPrompt,
    );
  }
}
