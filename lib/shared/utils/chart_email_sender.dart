import 'package:astreal/shared/utils/zodiac_utils.dart';
// import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:sweph/sweph.dart';

import '../../core/constants/astrology_constants.dart';
import '../../data/models/astrology/aspect_info.dart'; // 添加 AspectInfo 模型的引用
import '../../data/models/astrology/planet_position.dart';
import '../../data/models/user/birth_data.dart';

/// 星盤數據電子郵件發送器
class ChartEmailSender {
  /// 發送星盤數據
  static Future<bool> sendChartData({
    required BirthData birthData,
    required List<PlanetPosition> planets,
    required List<AspectInfo> aspects,
    required HouseCuspData housesData,
    String? recipientEmail,
    bool useHtml = false,
    double? latitude,
    double? longitude,
  }) async {
    // 如果沒有提供收件人郵箱，則使用默認郵箱
    final String recipient = recipientEmail ?? '<EMAIL>';

    // 生成郵件主題
    final String subject = '${birthData.name}的星盤數據';

    // 根據格式選擇生成郵件內容
    final String body = useHtml
        ? _generateHtmlContent(
            birthData, planets, aspects, housesData, latitude, longitude)
        : _generatePlainTextContent(
            birthData, planets, aspects, housesData, latitude, longitude);

    // // 創建郵件
    // final Email email = Email(
    //   recipients: [recipient],
    //   subject: subject,
    //   body: body,
    //   isHTML: useHtml,
    // );
    //
    // try {
    //   await FlutterEmailSender.send(email);
    //   return true;
    // } catch (error) {
    //   debugPrint('發送星盤數據郵件失敗: $error');
    //   return false;
    // }
    return false;
  }

  /// 生成純文本格式的郵件內容
  static String _generatePlainTextContent(
    BirthData birthData,
    List<PlanetPosition> planets,
    List<AspectInfo> aspects,
    HouseCuspData housesData,
    double? latitude,
    double? longitude,
  ) {
    final StringBuffer body = StringBuffer();

    // 添加基本信息
    body.writeln('姓名: ${birthData.name}');
    body.writeln('出生日期: ${_formatDateTime(birthData.dateTime)}');
    body.writeln('出生地點: ${birthData.birthPlace}');
    if (latitude != null && longitude != null) {
      body.writeln(
          '經緯度: ${latitude.toStringAsFixed(4)}°, ${longitude.toStringAsFixed(4)}°');
    }
    if (birthData.notes != null && birthData.notes!.isNotEmpty) {
      body.writeln('備註: ${birthData.notes}');
    }
    body.writeln('\n');

    // 添加行星位置信息
    body.writeln('===== 行星位置 =====');
    for (final planet in planets) {
      final signDegree = planet.longitude % 30;
      body.writeln(
          '${planet.name}: ${planet.sign} ${_formatDegree(signDegree)} (${planet.getHouseText(false)})');
    }
    body.writeln('\n');

    // 添加宮位信息
    body.writeln('===== 宮位信息 =====');
    for (int i = 1; i <= 12; i++) {
      final houseAngle = housesData.cusps[i];
      final sign = _getZodiacSign(houseAngle);
      final signDegree = houseAngle % 30;
      final houseDescription = ZodiacUtils.getHouseDescription(i);

      body.writeln('第$i宮: $sign ${_formatDegree(signDegree)}');
      body.writeln('含義: $houseDescription');

      // 添加此宮位的行星
      final planetsInHouse =
          planets.where((planet) => planet.house == i).toList();
      if (planetsInHouse.isNotEmpty) {
        body.writeln('此宮位的行星:');
        for (final planet in planetsInHouse) {
          body.writeln(
              '  ${planet.name} (${planet.sign} ${_formatDegree(planet.longitude % 30)})');
        }
      }
      body.writeln('');
    }

    // 添加相位信息
    body.writeln('===== 相位信息 =====');
    if (aspects.isEmpty) {
      body.writeln('沒有相位');
    } else {
      for (final aspect in aspects) {
        body.writeln(
            '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name}');
        body.writeln(
            '角度: ${aspect.angle}° (${aspect.orb.toStringAsFixed(2)}°)');
        body.writeln('');
      }
    }

    return body.toString();
  }

  /// 生成 HTML 格式的郵件內容
  static String _generateHtmlContent(
    BirthData birthData,
    List<PlanetPosition> planets,
    List<AspectInfo> aspects,
    HouseCuspData housesData,
    double? latitude,
    double? longitude,
  ) {
    final StringBuffer html = StringBuffer();

    // HTML 頭部
    html.writeln('''
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>${birthData.name}的星盤數據</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { color: #3498db; margin-top: 30px; border-left: 4px solid #3498db; padding-left: 10px; }
        .info-section { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .planet-item { margin-bottom: 10px; padding: 8px; border-bottom: 1px solid #eee; }
        .house-item { background-color: #f0f8ff; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
        .house-header { display: flex; align-items: center; margin-bottom: 10px; }
        .house-number { background-color: #3498db; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold; }
        .house-title { font-size: 18px; font-weight: bold; }
        .aspect-item { margin-bottom: 15px; padding: 10px; background-color: #f5f5f5; border-radius: 5px; }
        .planets-in-house { margin-top: 10px; padding-left: 15px; }
      </style>
    </head>
    <body>
      <h1>${birthData.name}的星盤數據</h1>

      <div class="info-section">
        <p><strong>姓名:</strong> ${birthData.name}</p>
        <p><strong>出生日期:</strong> ${_formatDateTime(birthData.dateTime)}</p>
        <p><strong>出生地點:</strong> ${birthData.birthPlace}</p>
    ''');

    if (latitude != null && longitude != null) {
      html.writeln(
          '<p><strong>經緯度:</strong> ${latitude.toStringAsFixed(4)}°, ${longitude.toStringAsFixed(4)}°</p>');
    }

    if (birthData.notes != null && birthData.notes!.isNotEmpty) {
      html.writeln('<p><strong>備註:</strong> ${birthData.notes}</p>');
    }

    html.writeln('</div>');

    // 行星位置部分
    html.writeln('<h2>行星位置</h2>');
    html.writeln('<div class="info-section">');

    for (final planet in planets) {
      final signDegree = planet.longitude % 30;
      html.writeln('''
        <div class="planet-item">
          <strong>${planet.name}:</strong> ${planet.sign} ${_formatDegree(signDegree)} (${planet.getHouseText()})
        </div>
      ''');
    }

    html.writeln('</div>');

    // 宮位信息部分
    html.writeln('<h2>宮位信息</h2>');

    for (int i = 1; i <= 12; i++) {
      final houseAngle = housesData.cusps[i];
      final sign = _getZodiacSign(houseAngle);
      final signDegree = houseAngle % 30;
      final houseDescription = ZodiacUtils.getHouseDescription(i);

      html.writeln('<div class="house-item">');
      html.writeln('''
        <div class="house-header">
          <div class="house-number">$i</div>
          <div class="house-title">第$i宮: $sign ${_formatDegree(signDegree)}</div>
        </div>
        <p><strong>含義:</strong> $houseDescription</p>
      ''');

      // 添加此宮位的行星
      final planetsInHouse =
          planets.where((planet) => planet.house == i).toList();
      if (planetsInHouse.isNotEmpty) {
        html.writeln('<div class="planets-in-house">');
        html.writeln('<p><strong>此宮位的行星:</strong></p>');
        html.writeln('<ul>');

        for (final planet in planetsInHouse) {
          html.writeln(
              '<li>${planet.name} (${planet.sign} ${_formatDegree(planet.longitude % 30)})</li>');
        }

        html.writeln('</ul>');
        html.writeln('</div>');
      }

      html.writeln('</div>');
    }

    // 相位信息部分
    html.writeln('<h2>相位信息</h2>');
    html.writeln('<div class="info-section">');

    if (aspects.isEmpty) {
      html.writeln('<p>沒有相位</p>');
    } else {
      for (final aspect in aspects) {
        html.writeln('''
          <div class="aspect-item">
            <p><strong>${aspect.planet1.name} ${aspect.aspect} ${aspect.planet2.name}</strong></p>
            <p>角度: ${aspect.angle}° (容許度: ${aspect.orb.toStringAsFixed(2)}°)</p>
          </div>
        ''');
      }
    }

    html.writeln('</div>');

    // HTML 尾部
    html.writeln('''
      <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
        <p>此郵件由 Astro Match 應用程序自動生成</p>
      </div>
    </body>
    </html>
    ''');

    return html.toString();
  }

  // 將度數轉換為度分秒格式
  static String _formatDegree(double degree) {
    final int deg = degree.floor();
    final double minDouble = (degree - deg) * 60;
    final int min = minDouble.floor();
    final int sec = ((minDouble - min) * 60).round();

    return '$deg°$min\'$sec"';
  }

  // 格式化日期時間
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} '
        '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 獲取星座
  static String _getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }
}
