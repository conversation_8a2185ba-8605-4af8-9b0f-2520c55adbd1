import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../../core/constants/astrology_constants.dart';
import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/aspect_info.dart'; // 添加 AspectInfo 模型的引用
import '../../data/models/astrology/chart_settings.dart';
import '../../data/models/astrology/planet_position.dart';
import '../../data/models/user/birth_data.dart';
import '../../data/services/api/astrology_service.dart';
import 'enhanced_timezone_service.dart';
import 'julian_date_utils.dart';

/// 占星計算工具類
/// 提供行星位置、宮位和相位計算的功能
class AstrologyCalculator {
  static bool _initialized = false;
  static bool _initializationFailed = false;

  // 使用新的 AstrologyService 实例
  static final AstrologyService _service = AstrologyService();

  /// 初始化 Swiss Ephemeris
  static Future<void> initialize() async {
    if (_initialized || _initializationFailed) {
      return;
    }

    try {
      // 使用 AstrologyService 的初始化方法
      await _service.initialize();
      _initialized = true;
      debugPrint('Swiss Ephemeris 初始化成功');
    } catch (e) {
      debugPrint('Swiss Ephemeris 初始化失敗: $e');
      // 標記初始化失敗，避免重複嘗試
      _initializationFailed = true;
    }
  }

  /// 計算上升點和宮位
  static Future<HouseCuspData?> calculateHouses(
      DateTime birthDateTime, double latitude, double longitude,
      {HouseSystem? houseSystem}) async {
    try {
      // 使用 AstrologyService 的計算宮位方法
      return await _service.calculateHouses(birthDateTime, latitude, longitude,
          houseSystem: houseSystem);
    } catch (e) {
      logger.e('計算宮位時出錯: $e');
      // 返回默認的等分宮位數據
      return null;
    }
  }

  /// 返回默認的行星位置
  static List<PlanetPosition> _getDefaultPlanetPositions(double ascendantDegree,
      [HouseCuspData? housesData, Map<String, bool>? planetVisibility]) {
    final List<PlanetPosition> positions = [];

    for (final planet in AstrologyConstants.PLANETS) {
      // 檢查行星是否應該顯示
      if (planetVisibility != null &&
          planetVisibility[planet['name'] as String] == false) {
        continue;
      }

      final longitude = (planet['id'] as int) * 30.0 % 360.0;
      int house;
      if (housesData != null && housesData.cusps.isNotEmpty) {
        // 如果有宮位數據，使用更精確的宮位計算方法
        house = _calculateHouseFromLongitude(longitude, housesData);
      } else {
        // 否則使用簡化的宮位計算方法
        house = _calculateHouse(longitude, ascendantDegree: ascendantDegree);
      }

      positions.add(PlanetPosition(
        id: planet['id'] as int,
        name: planet['name'] as String,
        symbol: planet['symbol'] as String,
        color: planet['color'] as Color,
        longitude: longitude,
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.0,
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: _getZodiacSign(longitude),
        house: house,
      ));
    }
    return positions;
  }

  /// 計算行星之間的互容接納關係
  static List<AspectInfo> calculateReceptions(List<PlanetPosition> planets) {
    // 使用 AstrologyService 的計算互容接納方法
    return _service.calculateReceptions(planets);
  }

  /// 計算相位
  static List<AspectInfo> calculateAspects(
    List<PlanetPosition> planets, {
    Map<String, double>? aspectOrbs,
  }) {
    // 使用 AstrologyService 的計算相位方法
    return _service.calculateAspects(planets, aspectOrbs: aspectOrbs);
  }

  /// 計算雙重星盤的相位（本命盤與行運盤之間的相位）
  static List<AspectInfo> calculateDualChartAspects(
    List<PlanetPosition> natalPlanets,
    List<PlanetPosition> transitPlanets, {
    Map<String, double>? aspectOrbs,
  }) {
    // 使用 AstrologyService 的計算雙重星盤相位方法
    return _service.calculateDualChartAspects(natalPlanets, transitPlanets,
        aspectOrbs: aspectOrbs);
  }

  /// 獲取星座
  static String _getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  /// 簡化的宮位計算（假設上升點在牡羊座0度）
  static int _calculateHouse(double longitude, {double ascendantDegree = 0.0}) {
    // 計算相對於上升點的位置
    double relativePosition = (longitude - ascendantDegree + 360) % 360;

    // 計算宮位（1-12）
    int house = ((relativePosition / 30) + 1).floor();

    // 確保宮位在1-12範圍內
    if (house <= 0) house += 12;
    if (house > 12) house %= 12;
    if (house == 0) house = 12;

    return house;
  }

  /// 獲取星座符號
  static String getZodiacSymbol(String sign) {
    return AstrologyConstants.ZODIAC_SYMBOLS[sign] ?? '?';
  }

  /// 獲取行星顏色
  static Color getPlanetColor(String planetName) {
    for (final planet in AstrologyConstants.PLANETS) {
      if (planet['name'] == planetName) {
        return planet['color'] as Color;
      }
    }
    return Colors.grey;
  }

  /// 獲取相位顏色
  static Color getAspectColor(String aspect) {
    for (final aspectType in AstrologyConstants.ASPECTS) {
      if (aspectType['name'] == aspect) {
        return aspectType['color'] as Color;
      }
    }
    return Colors.grey;
  }

  /// 計算次限推運日期
  static int calculateSecondaryProgressionYears(
      DateTime birthDate, DateTime targetDate) {
    // 計算從出生到目標日期的年數
    final years = targetDate.difference(birthDate).inDays / 365.25;

    // 次限推運使用「一天等於一年」的原則
    // 出生後的第N天的星象代表人生第N年的發展
    return years.round();
  }

  /// 計算兩個角度的中點（私有方法）
  static double _calculateMidpoint(double angle1, double angle2) {
    // 處理跨越 0 度的情況
    if ((angle1 - angle2).abs() > 180) {
      if (angle1 < angle2) {
        angle1 += 360;
      } else {
        angle2 += 360;
      }
    }

    double midpoint = (angle1 + angle2) / 2;
    if (midpoint >= 360) midpoint -= 360;

    return midpoint;
  }

  /// 計算兩個角度的中點（公開方法）
  ///
  /// 參數：
  /// - angle1: 第一個角度
  /// - angle2: 第二個角度
  ///
  /// 返回：兩個角度的中點
  static double calculateMidpoint(double angle1, double angle2) {
    return _calculateMidpoint(angle1, angle2);
  }

  /// 根據經度計算宮位（公開方法）
  ///
  /// 參數：
  /// - longitude: 行星經度
  /// - housesData: 宮位數據
  ///
  /// 返回：行星所在的宮位編號（1-12）
  static int calculateHouseFromLongitude(
      double longitude, HouseCuspData housesData) {
    return _calculateHouseFromLongitude(longitude, housesData);
  }

  /// 根據經度計算宮位（私有方法）
  static int _calculateHouseFromLongitude(
      double longitude, HouseCuspData housesData) {
    // 遍歷宮位數據，找出行星所在的宮位
    for (int i = 1; i <= 12; i++) {
      final houseStart = housesData.cusps[i];
      final houseEnd = housesData.cusps[i + 1 > 12 ? 1 : i + 1];

      // 處理跨越 0 度的宮位
      if (houseStart > houseEnd) {
        if (longitude >= houseStart || longitude < houseEnd) {
          return i;
        }
      } else {
        if (longitude >= houseStart && longitude < houseEnd) {
          return i;
        }
      }
    }

    // 默認返回第一宮
    return 1;
  }

  /// 計算次限推運日期（公開方法）
  ///
  /// 次限推運使用「一天等於一年」的原則
  /// 出生後的第N天的星象代表人生第N年的發展
  static DateTime calculateSecondaryProgressionDate(
      DateTime birthDate, DateTime targetDate) {
    logger.d('計算次限推運日期:');
    logger.d('出生日期: $birthDate');
    logger.d('目標日期: $targetDate');

    // 計算從出生到目標日的實際天數（包含小數）
    final Duration diff = targetDate.difference(birthDate);
    final double daysFromBirth = diff.inSeconds / Duration.secondsPerDay;
    logger.d('出生到目標日期的精確天數: $daysFromBirth');

    // 計算年齡
    final double ageInYears = daysFromBirth / 365.25;
    logger.d('年齡（精確）: $ageInYears 年');

    // 次限推運：出生後 ageInYears 天的星象代表人生第 ageInYears 年
    final double daysToAdd = ageInYears; // 不取整，保留小數

    final int wholeDays = daysToAdd.floor();
    final double fractionalDay = daysToAdd - wholeDays;

    final int hours = (fractionalDay * 24).floor();
    final int minutes = ((fractionalDay * 24 - hours) * 60).floor();
    final int seconds =
    ((((fractionalDay * 24 - hours) * 60) - minutes) * 60).round();

    final progressedDate = birthDate.add(Duration(
      days: wholeDays,
      hours: hours,
      minutes: minutes,
      seconds: seconds,
    ));

    logger.d('次限推運精確日期時間: $progressedDate');

    return progressedDate;
  }

  /// 計算三限推運日期（公開方法）
  ///
  /// 三限推運使用「一天等於一個月」的原則
  /// 出生後的第N天的星象代表人生第N個月的發展
  static DateTime calculateTertiaryProgressionDate(
      DateTime birthDate, DateTime targetDate) {
    logger.d('計算三限推運日期:');
    logger.d('出生日期: $birthDate');
    logger.d('目標日期: $targetDate');

    // 計算從出生到目標日的實際天數（包含小數）
    final Duration diff = targetDate.difference(birthDate);
    final double daysFromBirth = diff.inSeconds / Duration.secondsPerDay;
    logger.d('出生到目標日期的精確天數: $daysFromBirth');

    // 計算月齡（天數除以恆星月長度）
    // 使用恆星月長度 27.321661 天（月亮繪地球一圈的平均天數）
    final double ageInMonths =
        daysFromBirth / AstrologyConstants.SIDEREAL_MONTH;
    logger.d('月齡（精確）: $ageInMonths 月');
    logger.d('使用恆星月長度: ${AstrologyConstants.SIDEREAL_MONTH} 天');

    // 三限推運：出生後 ageInMonths 天的星象代表人生第 ageInMonths 個月
    final double daysToAdd = ageInMonths.round().toDouble(); // 四捨五入為整數月
    logger.d('需要添加的天數: $daysToAdd');

    final progressedDate = birthDate.add(Duration(days: daysToAdd.round()));
    logger.d('三限推運日期: $progressedDate');

    return progressedDate;
  }

  /// 計算太陽弧推運度數（公開方法）
  ///
  /// 太陽弧推運是以太陽的年度進度來推算所有行星的位置變化
  /// 參數：
  /// - birthDate: 出生日期時間
  /// - targetDate: 目標日期時間
  ///
  /// 返回：太陽弧推運的度數（每個行星需要前進的度數）
  static Future<double> calculateSolarArcDegrees(
      BirthData birthDate, DateTime targetDate) async {
    return await _calculateSolarArcDegrees(birthDate, targetDate);
  }

  /// 計算太陽返照日期（公開方法）
  ///
  /// 太陽返照是太陽回到出生時的確切位置時的星盤
  /// 參數：
  /// - birthDate: 出生日期時間
  /// - targetYear: 目標年份
  /// - sunLongitude: 出生時太陽經度
  ///
  /// 返回：太陽返照日期時間
  static Future<DateTime> calculateSolarReturnDate(
      BirthData birthDate, int targetYear, double sunLongitude) async {
    return await _calculateSolarReturnDate(birthDate, targetYear, sunLongitude);
  }

  /// 計算月亮返照日期（公開方法）
  ///
  /// 月亮返照是月亮回到出生時的確切位置時的星盤
  /// 參數：
  /// - birthDate: 出生日期時間
  /// - targetDate: 目標日期時間（通常是當月或下個月的日期）
  /// - moonLongitude: 出生時月亮經度
  ///
  /// 返回：月亮返照日期時間
  static Future<DateTime> calculateLunarReturnDate(
      BirthData birthDate, DateTime targetDate, double moonLongitude) async {
    return await _calculateLunarReturnDate(
        birthDate, targetDate, moonLongitude);
  }

  /// 計算太陽返照日期（私有方法）
  ///
  /// 太陽返照是太陽回到出生時的確切位置時的星盤
  static Future<DateTime> _calculateSolarReturnDate(
      BirthData birthDate, int targetYear, double sunLongitude) async {
    logger.d('計算太陽返照日期:');
    logger.d('出生日期: $birthDate');
    logger.d('目標年份: $targetYear');
    logger.d('出生時太陽經度: $sunLongitude');

    // 太陽返照通常發生在生日前後的幾天內
    // 我們先創建一個大約的日期範圍，然後在這個範圍內尋找太陽返照日期

    // 創建目標年份的生日日期
    final DateTime targetBirthday = DateTime(
      targetYear,
      birthDate.dateTime.month,
      birthDate.dateTime.day,
      birthDate.dateTime.hour,
      birthDate.dateTime.minute,
      birthDate.dateTime.second,
    );

    // 創建搜索範圍，生日前後15天
    final DateTime searchStart =
        targetBirthday.subtract(const Duration(days: 30));
    final DateTime searchEnd = targetBirthday.add(const Duration(days: 30));

    logger.d('搜索範圍開始: $searchStart');
    logger.d('搜索範圍結束: $searchEnd');

    // 初始化 Swiss Ephemeris
    await initialize();

    // 如果初始化失敗，返回目標生日
    if (!_initialized || _initializationFailed) {
      logger.w('Swiss Ephemeris 初始化失敗，返回目標生日');
      return targetBirthday;
    }

    // 使用二分法尋找太陽返照日期
    DateTime start = searchStart;
    DateTime end = searchEnd;
    // 初始化 mid 為目標生日，確保它有值
    DateTime mid = targetBirthday;
    double midSunLongitude = 0.0;

    // 設定精度關卡，當太陽經度與出生時太陽經度的差距小於這個值時停止搜索
    const double precision = 0.0001; // 精度為0.0001度

    int iterations = 0;
    const int maxIterations = 100; // 防止無限循環

    while (iterations < maxIterations) {
      iterations++;

      // 計算中間日期
      final int midMilliseconds =
          (start.millisecondsSinceEpoch + end.millisecondsSinceEpoch) ~/ 2;
      mid = DateTime.fromMillisecondsSinceEpoch(midMilliseconds);

      // 計算中間日期的太陽經度
      final julianDay = await JulianDateUtils.dateTimeToJulianDay(
        mid,
        birthDate.latitude, // 使用預設經緯度，因為我們只需要太陽經度
        birthDate.longitude,
      );

      CoordinatesWithSpeed result =
          _service.sweCalcUt(julianDay, HeavenlyBody.SE_SUN);
      midSunLongitude = result.longitude;

      // 計算太陽經度與目標經度的差距
      double diff = (midSunLongitude - sunLongitude).abs();
      if (diff > 180) diff = 360 - diff; // 處理跨越0度/360度的情況

      logger.d('迭代 $iterations: 日期=$mid, 太陽經度=$midSunLongitude, 差距=$diff');

      // 如果差距小於精度關卡，表示找到了太陽返照日期
      if (diff < precision) {
        logger.d('找到太陽返照日期: $mid');
        return mid;
      }

      // 根據太陽經度與目標經度的關係調整搜索範圍
      // 考慮太陽的進行方向（逆時針）
      if (_isClockwise(midSunLongitude, sunLongitude)) {
        end = mid;
      } else {
        start = mid;
      }
    }

    // 如果超過最大迭代次數仍未找到精確的太陽返照日期
    // 返回最接近的日期
    logger.w('超過最大迭代次數，返回最接近的日期');
    logger.d('最終使用的 mid 值: $mid');
    return mid;
  }

  /// 計算月亮返照日期（私有方法）
  ///
  /// 月亮返照是月亮回到出生時的確切位置時的星盤
  static Future<DateTime> _calculateLunarReturnDate(
      BirthData birthDate, DateTime targetDate, double moonLongitude) async {
    logger.d('計算月亮返照日期:');
    logger.d('出生日期: $birthDate');
    logger.d('目標日期: $targetDate');
    logger.d('出生時月亮經度: $moonLongitude');

    // 月亮返照大約每27.32天發生一次（朔望月週期）
    // 通常用戶想要的是最近一次已經發生的月亮返照，所以我們從目標日期往前搜索

    // 創建搜索範圍，從目標日期往前30天搜索（略大於一個朔望月週期）
    final DateTime searchStart = targetDate.subtract(const Duration(days: 28));
    final DateTime searchEnd = targetDate;

    logger.d('搜索範圍開始: $searchStart');
    logger.d('搜索範圍結束: $searchEnd');

    // 初始化 Swiss Ephemeris
    await initialize();

    // 如果初始化失敗，返回目標日期
    if (!_initialized || _initializationFailed) {
      logger.w('Swiss Ephemeris 初始化失敗，返回目標日期');
      return targetDate;
    }

    // 首先找到最接近目標日期且在目標日期之前的月亮返照日期
    // 月亮平均每天移動約13.2度，所以我們可以估算大概的日期
    DateTime bestDate = searchStart;
    double bestDiff = 360.0;

    // 在搜索範圍內每隔6小時檢查一次，找到最接近且在目標日期之前的日期
    DateTime current = searchStart;
    while (current.isBefore(searchEnd) || current.isAtSameMomentAs(searchEnd)) {
      try {
        final julianDay = await JulianDateUtils.dateTimeToJulianDay(
          current,
          birthDate.latitude,
          birthDate.longitude,
        );

        CoordinatesWithSpeed result = _service.sweCalcUt(julianDay, HeavenlyBody.SE_MOON);
        double currentMoonLongitude = result.longitude;

        double diff = (currentMoonLongitude - moonLongitude).abs();
        if (diff > 180) diff = 360 - diff;

        // 優先選擇在目標日期之前且最接近的日期
        if (diff < bestDiff && current.isBefore(targetDate)) {
          bestDiff = diff;
          bestDate = current;
        } else if (bestDate == searchStart && diff < bestDiff) {
          // 如果還沒找到在目標日期之前的日期，暫時記錄最接近的
          bestDiff = diff;
          bestDate = current;
        }
      } catch (e) {
        logger.w('計算月亮位置時出錯: $e');
      }

      current = current.add(const Duration(hours: 6));
    }

    // 使用二分法在最佳日期附近精確搜索
    DateTime start = bestDate.subtract(const Duration(hours: 12));
    DateTime end = bestDate.add(const Duration(hours: 12));
    DateTime mid = bestDate;
    double midMoonLongitude = 0.0;

    try {
      // 設定精度關卡，當月亮經度與出生時月亮經度的差距小於這個值時停止搜索
      const double precision = 0.001; // 精度為0.001度，約2分鐘的時間差

      int iterations = 0;
      const int maxIterations = 100; // 防止無限循環

      while (iterations < maxIterations) {
        iterations++;

        // 計算中間日期
        final int midMilliseconds =
            (start.millisecondsSinceEpoch + end.millisecondsSinceEpoch) ~/ 2;
        mid = DateTime.fromMillisecondsSinceEpoch(midMilliseconds);

        // 計算中間日期的月亮經度
        final julianDay = await JulianDateUtils.dateTimeToJulianDay(
          mid,
          birthDate.latitude, // 使用預設經緯度，因為我們只需要太陽經度
          birthDate.longitude,
        );

        CoordinatesWithSpeed result =
            _service.sweCalcUt(julianDay, HeavenlyBody.SE_MOON);
        midMoonLongitude = result.longitude;

        // 計算月亮經度與目標經度的差距
        double diff = (midMoonLongitude - moonLongitude).abs();
        if (diff > 180) diff = 360 - diff; // 處理跨越0度/360度的情況

        logger.d('迭代 $iterations: 日期=$mid, 月亮經度=$midMoonLongitude, 差距=$diff');

        // 如果差距小於精度關卡，表示找到了月亮返照日期
        if (diff < precision) {
          logger.d('找到月亮返照日期: $mid');
          return mid;
        }

        // 根據月亮經度與目標經度的關係調整搜索範圍
        // 月亮在黃道上順時針移動（經度遞增）
        // 如果當前月亮經度小於目標經度，需要往後找（時間增加）
        // 如果當前月亮經度大於目標經度，需要往前找（時間減少）
        double normalizedDiff = (moonLongitude - midMoonLongitude + 360) % 360;
        if (normalizedDiff > 180) {
          normalizedDiff -= 360;
        }

        if (normalizedDiff > 0) {
          // 目標經度在當前經度之前（順時針方向），需要往後找
          start = mid;
        } else {
          // 目標經度在當前經度之後（順時針方向），需要往前找
          end = mid;
        }
      }

      // 如果超過最大迭代次數仍未找到精確的月亮返照日期
      // 返回最接近的日期
      logger.w('超過最大迭代次數，返回最接近的日期');
      logger.d('最終使用的 mid 值: $mid');
    } catch (e) {
      logger.e('計算月亮返照日期出錯: $e');
      return mid;
    }
    return mid;
  }

  /// 判斷 current 到 target 是否需順時針移動（即太陽是否會走向 target）
  /// 用於決定二分搜尋中應該往哪一邊靠近
  static bool _isClockwise(double current, double target) {
    double diff = (target - current + 360) % 360;
    return diff > 180;
  }

  /// 計算太陽弧推運度數（私有方法）
  ///
  /// 太陽弧推運是以太陽的年度進度來推算所有行星的位置變化
  static Future<double> _calculateSolarArcDegrees(
      BirthData birthDate, DateTime targetDate) async {
    logger.d('計算太陽弧推運度數：');
    logger.d('出生日期：$birthDate');
    logger.d('目標日期：$targetDate');

    // 初始化 Swiss Ephemeris
    // await initialize();

    // 如果初始化失敗，返回預設值
    // if (!_initialized || _initializationFailed) {
    logger.w('Swiss Ephemeris 初始化失敗，返回預設太陽弧推運度數');
    // 計算年數差距，每年約 1 度
    final years = targetDate.difference(birthDate.dateTime).inDays / 365.25;
    return years;
  }
}
