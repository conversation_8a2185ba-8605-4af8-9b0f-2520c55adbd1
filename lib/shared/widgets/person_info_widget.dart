import 'package:astreal/presentation/viewmodels/chart_viewmodel.dart';
import 'package:flutter/material.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/user/birth_data.dart';
import '../../data/models/user/gender.dart';
import '../../data/services/api/astrology_service.dart';
import '../../presentation/pages/birth_data_form_page.dart';
import '../../presentation/themes/app_theme.dart';
import '../utils/enhanced_timezone_service.dart';
import '../utils/julian_date_utils.dart';

class PersonInfoWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  const PersonInfoWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      elevation: 3,
      shadowColor: AppColors.royalIndigo.withOpacity(0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12), // 重點：確保展開動畫邊角統一
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            animationDuration: const Duration(milliseconds: 300),
            expansionCallback: (int index, bool isExpanded) {
              viewModel.togglePersonInfoExpanded();
            },
            children: [
              ExpansionPanel(
                backgroundColor: Colors.white,
                canTapOnHeader: true,
                isExpanded: viewModel.isPersonInfoExpanded,
                headerBuilder: (context, isExpanded) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ListTile(
                      title: const Text(
                        '個人資料',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.royalIndigo.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.person,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                    ),
                  );
                },
                body: Container(
                  color: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionHeader(
                        context,
                        color: AppColors.solarAmber,
                        icon: Icons.person,
                        title: '主要人物',
                        backgroundColor: AppColors.solarAmber.withOpacity(0.1),
                        onEdit: () {},
                        birthData: viewModel.primaryPerson,
                      ),
                      const SizedBox(height: 4),
                      _buildInfoRow(Icons.person_outline, '姓名',
                          viewModel.primaryPerson.name),
                      const SizedBox(height: 4),
                      _buildBirthTimeInfoRow(context, viewModel.primaryPerson),
                      const SizedBox(height: 4),
                      _buildInfoRow(Icons.location_on, '出生地點',
                          viewModel.primaryPerson.birthPlace),
                      if (viewModel.primaryPerson.gender != null) ...[
                        const SizedBox(height: 4),
                        _buildInfoRow(
                          viewModel.primaryPerson.gender == Gender.male
                              ? Icons.male
                              : Icons.female,
                          '性別',
                          viewModel.primaryPerson.gender!.displayName,
                        ),
                      ],
                      const SizedBox(height: 4),
                      _buildInfoRow(Icons.public, '經緯度',
                          '${viewModel.primaryPerson.longitude.toStringAsFixed(4)}°, ${viewModel.primaryPerson.latitude.toStringAsFixed(4)}°'),
                      const SizedBox(height: 4),
                      _buildTimezoneInfoRow(viewModel.primaryPerson),
                      const SizedBox(height: 4),
                      if (viewModel.chartType.requiresTwoPersons &&
                          viewModel.secondaryPerson != null) ...[
                        const SizedBox(height: 12),
                        _buildSectionHeader(
                          context,
                          color: Colors.green,
                          icon: Icons.people,
                          title: '次要人物',
                          backgroundColor: Colors.green.withOpacity(0.1),
                          onEdit: () {},
                          birthData: viewModel.secondaryPerson,
                        ),
                        const SizedBox(height: 4),
                        _buildInfoRow(Icons.person_outline, '姓名',
                            viewModel.secondaryPerson!.name,
                            color: Colors.green.shade800),
                        const SizedBox(height: 4),
                        _buildBirthTimeInfoRow(
                            context, viewModel.secondaryPerson!,
                            color: Colors.green.shade800),
                        const SizedBox(height: 4),
                        _buildInfoRow(Icons.location_on, '出生地點',
                            viewModel.secondaryPerson!.birthPlace,
                            color: Colors.green.shade800),
                        if (viewModel.secondaryPerson!.gender != null) ...[
                          const SizedBox(height: 4),
                          _buildInfoRow(
                            viewModel.secondaryPerson!.gender == Gender.male
                                ? Icons.male
                                : Icons.female,
                            '性別',
                            viewModel.secondaryPerson!.gender!.displayName,
                            color: Colors.green.shade800,
                          ),
                        ],
                        const SizedBox(height: 4),
                        _buildInfoRow(Icons.public, '經緯度',
                            '${viewModel.secondaryPerson!.longitude.toStringAsFixed(4)}°, ${viewModel.secondaryPerson!.latitude.toStringAsFixed(4)}°',
                            color: Colors.green.shade800),
                        const SizedBox(height: 4),
                        _buildTimezoneInfoRow(viewModel.secondaryPerson!,
                            color: Colors.green.shade800),
                      ],
                      if (viewModel.chartType.requiresSpecificDate &&
                          viewModel.specificDate != null) ...[
                        const SizedBox(height: 12),
                        _buildSectionHeader(
                          context,
                          color: AppColors.royalIndigo,
                          icon: Icons.event,
                          title: '推運日期',
                          backgroundColor:
                              AppColors.royalIndigo.withOpacity(0.1),
                        ),
                        const SizedBox(height: 8),
                        _buildInfoRow(Icons.calendar_today, '日期',
                            viewModel.formatDateTime(viewModel.specificDate!),
                            color: AppColors.royalIndigo),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context, {
    required IconData icon,
    required String title,
    required Color color,
    Color? backgroundColor,
    Color? borderColor,
    VoidCallback? onEdit,
    BirthData? birthData,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: backgroundColor ?? color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: borderColor ?? color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 16,
              color: color,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
          // 編輯按鈕
          if (onEdit != null && birthData != null)
            Container(
              margin: const EdgeInsets.only(left: 8),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _handleEditBirthData(context, onEdit, birthData),
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: color.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.edit,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '編輯',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: color,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 處理編輯出生資料
  Future<void> _handleEditBirthData(
      BuildContext context, VoidCallback onEdit, BirthData birthData) async {
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => BirthDataFormPage(initialData: birthData),
      ),
    );

    if (result != null) {
      // 更新 ViewModel 中的出生資料
      if (birthData.id == viewModel.primaryPerson.id) {
        viewModel.setPrimaryPerson(result);
      } else if (viewModel.secondaryPerson != null &&
          birthData.id == viewModel.secondaryPerson!.id) {
        viewModel.setSecondaryPerson(result);
      }

      // 顯示成功訊息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('出生資料已更新'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    }
  }

  /// 建立信息行
  Widget _buildInfoRow(IconData icon, String label, String value,
      {Color? color}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Icon(icon, size: 14, color: color ?? AppColors.textDark),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 70,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: color ?? AppColors.textDark,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: color ?? AppColors.textDark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化出生時間，包含不確定標記
  String _formatBirthDateTime(BirthData birthData) {
    final dateTimeStr = viewModel.formatDateTime(birthData.dateTime);
    if (birthData.isTimeUncertain) {
      return '$dateTimeStr (時間不確定)';
    }
    return dateTimeStr;
  }

  /// 構建出生時間資訊行（包含時間不確定標示）
  Widget _buildBirthTimeInfoRow(BuildContext context, BirthData birthData,
      {Color? color}) {
    final dateTimeStr = viewModel.formatDateTime(birthData.dateTime);

    // 構建時間文字，包含不確定標示
    String timeText = dateTimeStr;
    if (birthData.isTimeUncertain) {
      timeText += ' (時間不確定)';
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Icon(Icons.calendar_today,
                size: 14, color: color ?? AppColors.textDark),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 70,
            child: Text(
              '出生時間',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: color ?? AppColors.textDark,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    timeText,
                    style: TextStyle(
                      fontSize: 14,
                      color: birthData.isTimeUncertain
                          ? Colors.orange[700]
                          : (color ?? AppColors.textDark),
                      fontWeight: birthData.isTimeUncertain
                          ? FontWeight.w500
                          : FontWeight.normal,
                    ),
                  ),
                ),
                if (birthData.isTimeUncertain) ...[
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: () => _showTimeUncertaintyDialog(context),
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.help_outline,
                        size: 12,
                        color: Colors.orange,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建時區資訊行
  Widget _buildTimezoneInfoRow(BirthData birthData, {Color? color}) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getTimezoneInfo(birthData),

      builder: (context, snapshot) {
        String timezoneText;
        if (snapshot.connectionState == ConnectionState.waiting) {
          timezoneText = '載入中...';
        } else if (snapshot.hasError || !snapshot.hasData) {
          // 如果無法獲取時區，使用簡化的 UTC 偏移計算
          final timezoneOffset = (birthData.longitude / 15).round();
          final sign = timezoneOffset >= 0 ? '+' : '';
          timezoneText = 'UTC$sign$timezoneOffset (估算)';
        } else {
          final data = snapshot.data!;
          final timezoneName = data['name'] as String?;
          final offset = data['offset'] as double?;

          if (timezoneName != null && offset != null) {
            // 格式化偏移量顯示
            final sign = offset >= 0 ? '+' : '';
            final offsetStr = offset % 1 == 0
                ? '${offset.toInt()}'
                : offset.toStringAsFixed(1);
            timezoneText = '$timezoneName (UTC$sign$offsetStr)';
          } else if (timezoneName != null) {
            timezoneText = timezoneName;
          } else {
            final timezoneOffset = (birthData.longitude / 15).round();
            final sign = timezoneOffset >= 0 ? '+' : '';
            timezoneText = 'UTC$sign$timezoneOffset (估算)';
          }
        }

        return Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Icon(Icons.access_time,
                    size: 14, color: color ?? AppColors.textDark),
              ),
              const SizedBox(width: 8),
              SizedBox(
                width: 70,
                child: Text(
                  '時區',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: color ?? AppColors.textDark,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  timezoneText,
                  style: TextStyle(
                    fontSize: 14,
                    color: color ?? AppColors.textDark,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 獲取時區信息和偏移量
  Future<Map<String, dynamic>> _getTimezoneInfo(BirthData birthData) async {
    try {
      // 獲取時區名稱
      final timezoneName = await EnhancedTimezoneService().getTimeZoneFromLatLng(
        birthData.latitude,
        birthData.longitude,
      );

      // 使用 JulianDateUtils 計算精確的時區偏移量
      final offset = await JulianDateUtils.calculateZonedDateTimeOffset(
        birthData.latitude,
        birthData.longitude,
        birthData.dateTime.year,
        birthData.dateTime.month,
        birthData.dateTime.day,
        birthData.dateTime.hour,
        birthData.dateTime.minute,
      );

      return {
        'name': timezoneName,
        'offset': offset,
      };
    } catch (e) {
      logger.e('獲取時區信息時出錯: $e');
      return {};
    }
  }

  /// 顯示出生時間不確定說明對話框
  void _showTimeUncertaintyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('出生時間不確定的影響'),
            ],
          ),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '當出生時間不確定時，以下星盤要素可能會受到影響：',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 12),
                Text('• 上升星座（需要精確的出生時間）'),
                Text('• 宮位分析（宮位邊界會因時間而變化）'),
                Text('• 月亮星座（如果時間差距較大）'),
                Text('• 行星宮位位置'),
                Text('• 相位的精確度'),
                Text('• 中天星座和天底星座'),
                SizedBox(height: 12),
                Text(
                  '建議：',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 8),
                Text('• 盡量確認準確的出生時間'),
                Text('• 重點關注太陽星座和主要行星位置'),
                Text('• 參考星盤時保持適當的彈性'),
                Text('• 可以使用太陽星座為主的分析方法'),
                SizedBox(height: 12),
                Text(
                  '注意：星盤顯示會以提供的時間為準，但解讀時請考慮時間不確定的因素。',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('了解'),
            ),
          ],
        );
      },
    );
  }
}
