import 'package:astreal/presentation/pages/main/files_page.dart';
import 'package:astreal/presentation/viewmodels/recent_charts_viewmodel.dart';
import 'package:astreal/shared/utils/chart_email_sender.dart';
import 'package:astreal/shared/utils/chart_pdf_generator.dart';
import 'package:clipboard/clipboard.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/chart_theme_analysis_service.dart';
import '../../data/services/api/firdaria_service.dart';
import '../../data/services/api/profection_service.dart';
import '../../shared/utils/aspect_distributor.dart';
import '../../shared/utils/chart_info_generator.dart';

// 用于管理星盘数据和相关操作。
class ChartViewModel extends ChangeNotifier {
  // 使用 AstrologyService 處理占星相關的計算。
  final AstrologyService _astrologyService = AstrologyService();

  // 用於追蹤異步操作的加載狀態。
  bool _isLoading = true;

  // 用於追蹤電子郵件發送狀態。
  bool _isSendingEmail = false;

  // 用於追蹤 PDF 生成狀態。
  bool _isGeneratingPdf = false;

  // 用於追蹤複製操作狀態。
  bool _isCopying = false;

  // 用於控制個人資訊是否展開顯示。
  bool _isPersonInfoExpanded = true;

  // 用於顯示位置資訊的狀態。
  String _locationStatus = '';

  // 用於追蹤星盤變更的唯一標識符
  int _chartUpdateCounter = 0;

  // 返照盤比較模式狀態
  bool _isReturnChartComparisonMode = false;

  // Data（數據）
  // 儲存星盤的主要數據。
  ChartData _chartData = ChartData(
    chartType: ChartType.natal,
    primaryPerson: BirthData(
      id: '',
      name: '',
      dateTime: DateTime.now(),
      birthPlace: '',
      latitude: 0.0,
      longitude: 0.0,
    ),
  );

  // 儲存星盤的類型。
  ChartType _chartType = ChartType.natal;

  // 儲存主要人物的出生數據。
  BirthData _primaryPerson = BirthData(
    id: '',
    name: '',
    dateTime: DateTime.now(),
    birthPlace: '',
    latitude: 0.0,
    longitude: 0.0,
  );

  // 儲存次要人物的出生數據（如果有的話）。
  BirthData? _secondaryPerson;

  // 儲存特定日期，用於某些星盤類型。
  DateTime? _specificDate;

  // 儲存本命行星的位置。
  final List<PlanetPosition> _natalPlanets = [];

  // 法達盤數據
  List<FirdariaData>? _firdariaData;

  // 選中的法達盤週期索引
  int? _selectedFirdariaPeriodIndex;

  // 小限法數據
  ProfectionTimelineResult? _profectionData;

  // 是否為白天出生
  bool _isDaytimeBirth = true;

  // 儲存緯度。
  double? _latitude;

  // 儲存經度。
  double? _longitude;

  // 設定 ViewModel
  SettingsViewModel? _settingsViewModel;

  // 星盤設定
  ChartSettings? _chartSettings;

  // 返回加載狀態。
  bool get isLoading => _isLoading;

  // 返回電子郵件發送狀態。
  bool get isSendingEmail => _isSendingEmail;

  // 返回 PDF 生成狀態。
  bool get isGeneratingPdf => _isGeneratingPdf;

  // 返回複製狀態。
  bool get isCopying => _isCopying;

  // 返回個人資訊是否展開的狀態。
  bool get isPersonInfoExpanded => _isPersonInfoExpanded;

  // 返回位置資訊的狀態。
  String get locationStatus => _locationStatus;

  // 返回星盤更新計數器
  int get chartUpdateCounter => _chartUpdateCounter;

  // 返回星盤數據。
  ChartData get chartData => _chartData;

  // 返回星盤類型。
  ChartType get chartType => _chartType;

  // 返回主要人物的出生數據。
  BirthData get primaryPerson => _primaryPerson;

  // 返回次要人物的出生數據。
  BirthData? get secondaryPerson => _secondaryPerson;

  // 返回特定日期。
  DateTime? get specificDate => _specificDate;

  // 返回本命行星的位置。
  List<PlanetPosition> get natalPlanets => _natalPlanets;

  // 返回緯度。
  double? get latitude => _latitude;

  // 返回經度。
  double? get longitude => _longitude;

  // 返回設定 ViewModel
  SettingsViewModel? get settingsViewModel => _settingsViewModel;

  // 返回星盤設定
  ChartSettings? get chartSettings => _chartSettings;

  // 返回法達盤數據
  List<FirdariaData>? get firdariaData => _firdariaData;

  // 設置法達盤數據
  set firdariaData(List<FirdariaData>? data) {
    _firdariaData = data;
    chartData.firdariaData = data;
    notifyListeners();
  }

  // 返回小限法數據
  ProfectionTimelineResult? get profectionData => _profectionData;

  // 設置小限法數據
  set profectionData(ProfectionTimelineResult? data) {
    _profectionData = data;
    notifyListeners();
  }

  // 獲取包含相位信息的本命盤行星列表（不可修改）
  List<PlanetPosition> get natalPlanetsWithAspects =>
      List.unmodifiable(_natalPlanets);

  // 獲取包含相位信息的行運/推運行星列表（不可修改）
  List<PlanetPosition> get transitPlanetsWithAspects =>
      _chartData.planets != null ? List.unmodifiable(_chartData.planets!) : [];

  // 檢查是否有本命盤數據
  bool get hasNatalData => _natalPlanets.isNotEmpty;

  // 檢查是否有行運/推運數據
  bool get hasTransitData =>
      _chartData.planets != null && _chartData.planets!.isNotEmpty;

  // 獲取本命盤行星的相位統計
  Map<String, dynamic> get natalAspectStatistics =>
      hasNatalData ? AspectDistributor.getAspectStatistics(_natalPlanets) : {};

  // 獲取行運/推運行星的相位統計
  Map<String, dynamic> get transitAspectStatistics => hasTransitData
      ? AspectDistributor.getAspectStatistics(_chartData.planets!)
      : {};

  // 返回選中的法達盤週期索引
  int? get selectedFirdariaPeriodIndex => _selectedFirdariaPeriodIndex;

  // 設置選中的法達盤週期索引
  set selectedFirdariaPeriodIndex(int? index) {
    _selectedFirdariaPeriodIndex = index;
    notifyListeners();
  }

  // 返回是否為白天出生
  bool get isDaytimeBirth => _isDaytimeBirth;

  // 設置是否為白天出生
  set isDaytimeBirth(bool value) {
    _isDaytimeBirth = value;
    notifyListeners();
  }

  // 返回返照盤比較模式狀態
  bool get isReturnChartComparisonMode => _isReturnChartComparisonMode;

  // 設置返照盤比較模式
  void setReturnChartComparisonMode(bool value) {
    if (_isReturnChartComparisonMode != value) {
      _isReturnChartComparisonMode = value;
      logger.i('返照盤比較模式切換為: $value');
      if (value) {
        ChartData chartDataTemp = ChartData(
          chartType: ChartType.synastry,
          primaryPerson: _primaryPerson,
          secondaryPerson: BirthData(
            id: 'return_date_${chartData.returnDate!.millisecondsSinceEpoch}',
            name: '返照日期',
            dateTime: chartData.returnDate!,
            birthPlace: chartData.primaryPerson.birthPlace,
            latitude: chartData.primaryPerson.latitude,
            longitude: chartData.primaryPerson.longitude,
          ),
        );

        _primaryPerson = chartDataTemp.primaryPerson;
        _secondaryPerson = chartDataTemp.secondaryPerson;
        calculateChart(chartData: chartDataTemp);
        _chartType = ChartType.synastry;
      } else {
        ChartData chartDataTemp = ChartData(
          chartType: ChartType.solarReturn,
          primaryPerson: _primaryPerson,
          specificDate: specificDate,
        );
        calculateChart(chartData: chartDataTemp);
        _primaryPerson = chartDataTemp.primaryPerson;
        _secondaryPerson = null;
        _chartType = ChartType.solarReturn;
      }

      notifyListeners();
    }
  }

  // 設置設定 ViewModel
  void setSettingsViewModel(SettingsViewModel settingsViewModel) {
    _settingsViewModel = settingsViewModel;
  }

  /// 獲取本命盤中特定行星的相位信息
  ///
  /// [planetName] 行星名稱
  /// 返回包含相位信息的行星對象，如果找不到則返回 null
  PlanetPosition? getNatalPlanetWithAspects(String planetName) {
    try {
      return _natalPlanets.firstWhere((planet) => planet.name == planetName);
    } catch (e) {
      return null;
    }
  }

  /// 獲取行運/推運盤中特定行星的相位信息
  ///
  /// [planetName] 行星名稱
  /// 返回包含相位信息的行星對象，如果找不到則返回 null
  PlanetPosition? getTransitPlanetWithAspects(String planetName) {
    if (_chartData.planets == null) return null;

    try {
      return _chartData.planets!
          .firstWhere((planet) => planet.name == planetName);
    } catch (e) {
      return null;
    }
  }

  /// 獲取兩個行星之間的相位信息
  ///
  /// [planet1Name] 第一個行星名稱
  /// [planet2Name] 第二個行星名稱
  /// [fromNatal] 是否從本命盤中查找第一個行星
  /// 返回相位信息列表
  List<AspectInfo> getAspectsBetweenPlanets(
      String planet1Name, String planet2Name,
      {bool fromNatal = true}) {
    PlanetPosition? planet1;

    if (fromNatal) {
      planet1 = getNatalPlanetWithAspects(planet1Name);
    } else {
      planet1 = getTransitPlanetWithAspects(planet1Name);
    }

    if (planet1 == null) return [];

    return planet1.getAspectsWithPlanet(planet2Name);
  }

  /// 獲取所有本命盤與行運盤之間的相位
  ///
  /// 返回本命盤行星與行運盤行星之間的所有相位
  List<AspectInfo> getAllNatalTransitAspects() {
    final allAspects = <AspectInfo>[];

    return allAspects;
  }

  /// 獲取所有行運盤與本命盤之間的相位
  ///
  /// 返回行運盤行星與本命盤行星之間的所有相位
  List<AspectInfo> getAllTransitNatalAspects() {
    if (_chartData.planets == null) return [];

    final allAspects = <AspectInfo>[];

    return allAspects;
  }

  // Constructor（構造函數）
  // 默認構造函數
  ChartViewModel() {
    // 創建一個默認的 ChartData 對象
    _chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: BirthData(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: '',
        dateTime: DateTime.now(),
        birthPlace: '',
        latitude: 0,
        longitude: 0,
      ),
    );
    _chartType = _chartData.chartType;
    _primaryPerson = _chartData.primaryPerson;
  }

  // 構造函數，接收初始星盤數據。
  ChartViewModel.withChartData(
      {required ChartData initialChartData, BuildContext? context}) {
    _chartData = initialChartData;
    _chartType = initialChartData.chartType;
    _primaryPerson = initialChartData.primaryPerson;
    _secondaryPerson = initialChartData.secondaryPerson;
    _specificDate = initialChartData.specificDate;

    // 計算星盤數據
    calculateChart();

    // 記錄到最近使用的星盤
    if (context != null) {
      _addToRecentCharts(context);
    }
  }

  String getChartTitle() {
    final primaryName = primaryPerson.name;
    final secondaryName = secondaryPerson?.name;

    switch (chartType) {
      case ChartType.mundane:
      case ChartType.equinoxSolstice:
        return primaryName;
      case ChartType.natal:
        return '$primaryName 的 ${chartType.name}';
      default:
        if (chartType.requiresTwoPersons && secondaryPerson != null) {
          return '$primaryName 與 $secondaryName 的${chartType.name}';
        } else {
          return '$primaryName的${chartType.name}';
        }
    }
  }

  /// 將當前星盤添加到最近使用記錄
  void _addToRecentCharts(BuildContext context) {
    try {
      final recentChartsViewModel =
          Provider.of<RecentChartsViewModel>(context, listen: false);
      recentChartsViewModel.addOrUpdateRecentChart(_chartData);
      logger.d('已將星盤添加到最近使用記錄');
    } catch (e) {
      logger.e('添加到最近使用記錄時出錯: $e');
    }
  }

  /// 設置主要人物的出生數據
  void setPrimaryPerson(BirthData birthData) {
    _primaryPerson = birthData;
    // 創建新的 ChartData 對象
    _chartData = ChartData(
      chartType: _chartType,
      primaryPerson: birthData,
      secondaryPerson: _secondaryPerson,
      specificDate: _specificDate,
      planets: _chartData.planets,
      houses: _chartData.houses,
      aspects: _chartData.aspects,
      arabicPoints: _chartData.arabicPoints,
    );
    _latitude = birthData.latitude;
    _longitude = birthData.longitude;

    // 重新計算星盤數據
    calculateChart();
  }

  /// 設置次要人物的出生數據
  void setSecondaryPerson(BirthData? birthData) {
    _secondaryPerson = birthData;
    // 創建新的 ChartData 對象
    _chartData = ChartData(
      chartType: _chartType,
      primaryPerson: _primaryPerson,
      secondaryPerson: birthData,
      specificDate: _specificDate,
      planets: _chartData.planets,
      houses: _chartData.houses,
      aspects: _chartData.aspects,
      arabicPoints: _chartData.arabicPoints,
    );

    // 重新計算星盤數據
    calculateChart();
  }

  /// 交換主要人物和次要人物
  void swapPrimaryAndSecondaryPersons() {
    if (_secondaryPerson == null) {
      logger.w('無法交換：沒有次要人物資料');
      return;
    }

    logger.i('交換主要人物和次要人物');

    // 暫存主要人物資料
    final tempPrimary = _primaryPerson;

    // 交換資料
    _primaryPerson = _secondaryPerson!;
    _secondaryPerson = tempPrimary;

    // 創建新的 ChartData 對象
    _chartData = ChartData(
      chartType: _chartType,
      primaryPerson: _primaryPerson,
      secondaryPerson: _secondaryPerson,
      specificDate: _specificDate,
      planets: _chartData.planets,
      houses: _chartData.houses,
      aspects: _chartData.aspects,
      arabicPoints: _chartData.arabicPoints,
    );

    // 更新經緯度
    _latitude = _primaryPerson.latitude;
    _longitude = _primaryPerson.longitude;

    // 重新計算星盤數據
    calculateChart();

    logger.i('主次要人物交換完成：${_primaryPerson.name} ↔ ${_secondaryPerson?.name}');
  }

  /// 設置特定日期（用於推運盤和返照盤）
  void setSpecificDate(DateTime date) {
    logger.i('設置特定日期: $date');
    _specificDate = date;
    // 創建新的 ChartData 對象
    _chartData = ChartData(
      chartType: _chartType,
      primaryPerson: _primaryPerson,
      secondaryPerson: _secondaryPerson,
      specificDate: date,
      planets: _chartData.planets,
      houses: _chartData.houses,
      aspects: _chartData.aspects,
      arabicPoints: _chartData.arabicPoints,
    );

    // 重新計算星盤數據
    calculateChart();
  }

  /// 計算法達盤數據
  Future<void> calculateFirdaria() async {
    logger.i('開始計算法達盤數據');
    setLoading(true);

    try {
      // 確定要使用的當前日期：如果有推運時間則使用推運時間，否則使用現在時間
      final currentDate = _specificDate ?? DateTime.now();
      logger.i('法達盤計算使用的當前日期: $currentDate');

      // 使用 FirdariaService 計算法達盤數據
      _firdariaData = await FirdariaService().calculateFirdaria(
        _primaryPerson,
        currentDate: currentDate,
      );

      chartData.firdariaData = _firdariaData;
      logger.i('法達盤數據計算完成，共 ${_firdariaData?.length} 個週期');

      // 如果有法達盤數據，選擇當前週期
      if (_firdariaData != null && _firdariaData!.isNotEmpty) {
        // 找到當前週期的索引
        final currentIndex =
            _firdariaData!.indexWhere((period) => period.isCurrent);
        if (currentIndex >= 0) {
          _selectedFirdariaPeriodIndex = currentIndex;
        }
      }
    } catch (e, stackTrace) {
      logger.e('計算法達盤時出錯: $e');
      logger.e('堆疊追蹤: $stackTrace');
    } finally {
      setLoading(false);
      notifyListeners(); // 確保 UI 更新
    }
  }

  /// 計算小限法數據
  Future<void> calculateProfection() async {
    logger.i('開始計算小限法數據');
    setLoading(true);

    try {
      // 確定要使用的當前日期：如果有推運時間則使用推運時間，否則使用現在時間
      final currentDate = _specificDate ?? DateTime.now();
      logger.i('小限法計算使用的當前日期: $currentDate');

      // 使用 ProfectionService 計算小限法數據
      final profectionService = ProfectionService();
      _profectionData = await profectionService.calculateProfectionTimeline(
        _primaryPerson,
        currentDate: currentDate,
        yearsRange: 10, // 計算前後各10年
        housesData: _chartData.houses, // 傳遞本命盤宮位數據
      );

      logger.i(
          '小限法數據計算完成，當前年齡: ${_profectionData?.currentProfection.currentAge}歲');
    } catch (e, stackTrace) {
      logger.e('計算小限法時出錯: $e');
      logger.e('堆疊追蹤: $stackTrace');
    } finally {
      setLoading(false);
      notifyListeners(); // 確保 UI 更新
    }
  }

  Future<ChartData> calculateChart({ChartData? chartData, HouseSystem? houseSystem}) async {
    final dataToUse = chartData ?? _chartData;
    _chartData = await _doCalculate(dataToUse);
    notifyListeners();
    return _chartData;
  }

  Future<ChartData> _doCalculate(ChartData data) async {
    logger.i('開始計算${_chartType.name}星盤數據');
    setLoading(true);
    try {
      _chartSettings = await ChartSettings.loadFromPrefs();

      final result = await AstrologyService().calculateChartData(
        data,
        chartSettings: _chartSettings,
      );
      logger.d('基本星盤數據計算完成');
      logger.i('${_chartType.name}星盤數據計算完成');
      return result;
    } catch (e, stackTrace) {
      logger.e('計算星盤時出錯: $e');
      logger.e('堆疊追蹤: $stackTrace');
      return data;
    } finally {
      setLoading(false);
      notifyListeners(); // 保險起見還是呼叫一次 UI 更新
    }
  }

  /// 計算星盤數據
  ///
  /// 此方法執行以下操作：
  /// 1. 設置加載狀態為「正在加載」
  /// 2. 加載用戶的星盤設定（包括行星可見性設定）
  /// 3. 使用 ChartService 計算行星位置、宮位和相位
  /// 4. 計算行星之間的互容接納關係
  /// 5. 對特定星盤類型進行額外計算（如推運盤和返照盤）
  /// 6. 設置加載狀態為「完成」
  // Future<void> calculateChart({HouseSystem? houseSystem}) async {
  //   logger.i('開始計算${_chartType.name}星盤數據');
  //   setLoading(true);
  //   try {
  //     // 加載用戶的星盤設定
  //     _chartSettings = await ChartSettings.loadFromPrefs();
  //
  //     // 計算行星位置、宮位和相位，並傳遞設定
  //     _chartData = await AstrologyService().calculateChartData(
  //       chartData,
  //       chartSettings: _chartSettings,
  //     );
  //     logger.d('基本星盤數據計算完成');
  //
  //     logger.i('${_chartType.name}星盤數據計算完成');
  //
  //     // 通知 UI 更新
  //     notifyListeners();
  //   } catch (e, stackTrace) {
  //     logger.e('計算星盤時出錯: $e');
  //     logger.e('堆疊追蹤: $stackTrace');
  //   } finally {
  //     setLoading(false);
  //     notifyListeners(); // 確保 UI 更新
  //   }
  // }

  // Future<ChartData> calculate(ChartData chartData,
  //     {HouseSystem? houseSystem}) async {
  //   logger.i('開始計算${_chartType.name}星盤數據');
  //   setLoading(true);
  //   try {
  //     // 加載用戶的星盤設定
  //     _chartSettings = await ChartSettings.loadFromPrefs();
  //     // 計算行星位置、宮位和相位，並傳遞設定
  //     _chartData = await AstrologyService().calculateChartData(
  //       chartData,
  //       chartSettings: _chartSettings,
  //     );
  //     logger.d('基本星盤數據計算完成');
  //
  //     logger.i('${_chartType.name}星盤數據計算完成');
  //   } catch (e, stackTrace) {
  //     logger.e('計算星盤時出錯: $e');
  //     logger.e('堆疊追蹤: $stackTrace');
  //   } finally {
  //     setLoading(false);
  //     notifyListeners(); // 確保 UI 更新
  //   }
  //   return chartData;
  // }

  static Future<ChartData> calculateChartData(ChartData chartData) async {
    logger.i('開始計算${chartData.chartType.name}星盤數據');
    try {
      // 加載用戶的星盤設定
      ChartSettings chartSettings = await ChartSettings.loadFromPrefs();
      // 計算行星位置、宮位和相位，並傳遞行星可見性設定和相位容許度設定
      chartData = await AstrologyService()
          .calculateChartData(chartData, chartSettings: chartSettings);

      logger.i('星盤數據計算完成');
    } catch (e, stackTrace) {
      logger.e('計算星盤時出錯: $e');
      logger.e('堆疊追蹤: $stackTrace');
    }
    return chartData;
  }

  // Setters（Setter 方法）
  // 設置加載狀態並通知監聽器。
  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  // 設置電子郵件發送狀態並通知監聽器。
  void setSendingEmail(bool sending) {
    _isSendingEmail = sending;
    notifyListeners();
  }

  // 設置 PDF 生成狀態並通知監聽器。
  void setGeneratingPdf(bool generating) {
    _isGeneratingPdf = generating;
    notifyListeners();
  }

  // 設置複製狀態並通知監聽器。
  void setCopying(bool copying) {
    _isCopying = copying;
    notifyListeners();
  }

  // 切換個人資訊的展開/摺疊狀態並通知監聽器。
  void togglePersonInfoExpanded() {
    _isPersonInfoExpanded = !_isPersonInfoExpanded;
    notifyListeners();
  }

  // 設置星盤類型並重新計算星盤。
  Future<void> setChartType(ChartType type, {BuildContext? context}) async {
    if (type != _chartType) {
      logger.i('切換星盤類型：從 ${_chartType.name} 到 ${type.name}');

      // 如果新的星盤類型需要兩個人，但目前沒有第二個人的資料
      if (type.requiresTwoPersons &&
          _secondaryPerson == null &&
          context != null) {
        // 顯示出生資料選擇對話框
        final selectedPerson = await showPersonSelectionDialog(context);

        // 如果用戶取消了選擇，則不更改星盤類型
        if (selectedPerson == null) {
          logger.w('用戶取消了選擇第二個人，不更改星盤類型');
          return;
        }

        // 設置第二個人的資料
        _secondaryPerson = selectedPerson;
        _chartData.secondaryPerson = selectedPerson;
        logger.d('已設置第二個人的資料：${selectedPerson.name}');
      }

      // 如果新的星盤類型不需要第二個人，清除第二個人的資料
      if (!type.requiresTwoPersons && _secondaryPerson != null) {
        logger.d('清除第二個人的資料，因為新星盤類型不需要');
        _secondaryPerson = null;
        _chartData.secondaryPerson = null;
      }

      // 更新星盤類型
      _chartType = type;
      _chartData.chartType = type;

      // 為返照盤類型設定目標日期
      if (type.isReturnChart) {
        // 如果沒有設定特定日期，使用當前日期作為目標日期
        if (_chartData.specificDate == null) {
          _chartData.specificDate = DateTime.now();
          logger.d('為返照盤設定目標日期: ${_chartData.specificDate}');
        }
      }

      // 清空現有的行星、宮位和相位數據，確保完全重新計算
      _chartData.planets = [];
      _chartData.houses = null;
      _chartData.aspects = [];
      _chartData.arabicPoints = [];

      // 設置加載狀態並通知 UI 更新
      setLoading(true);
      notifyListeners();

      // 如果是法達盤，計算法達盤數據
      if (type == ChartType.firdaria) {
        await calculateChart(); // 先計算基本星盤數據
        await calculateFirdaria(); // 再計算法達盤數據
      } else if (type == ChartType.profection) {
        await calculateChart(); // 先計算基本星盤數據
        await calculateProfection(); // 再計算小限法數據
      } else {
        // 重新計算星盤數據
        await calculateChart();
      }

      // 記錄到最近使用的星盤
      if (context != null) {
        _addToRecentCharts(context);
      }

      // 確保 UI 更新
      notifyListeners();
      logger.i('星盤類型切換完成：${type.name}');
    }
  }

  /// 使用新的 ChartData 更新星盤
  Future<void> updateChartData(ChartData newChartData,
      {BuildContext? context}) async {
    try {
      logger.i('使用新的 ChartData 更新星盤: ${newChartData.chartType.name}');

      // 更新星盤數據
      _chartData = newChartData;

      // 更新相關屬性
      _chartType = newChartData.chartType;
      _primaryPerson = newChartData.primaryPerson;
      _secondaryPerson = newChartData.secondaryPerson;
      _specificDate = newChartData.specificDate;
      // 清空現有的行星、宮位和相位數據，確保完全重新計算
      _chartData.planets = [];
      _chartData.houses = null;
      _chartData.aspects = [];
      _chartData.arabicPoints = [];
      _natalPlanets.clear();

      // 設置加載狀態
      setLoading(true);
      notifyListeners();

      // 如果是法達盤，計算法達盤數據
      if (_chartType == ChartType.firdaria) {
        await calculateChart(); // 先計算基本星盤數據
        await calculateFirdaria(); // 再計算法達盤數據
      } else if (_chartType == ChartType.profection) {
        await calculateChart(); // 先計算基本星盤數據
        await calculateProfection(); // 再計算小限法數據
      } else {
        // 重新計算星盤數據
        await calculateChart();
      }

      // 記錄到最近使用的星盤
      if (context != null) {
        _addToRecentCharts(context);
      }

      // 確保 UI 更新
      notifyListeners();
      logger.i('星盤數據更新完成: ${_chartType.name}');
    } catch (e, stackTrace) {
      logger.e('更新星盤數據時出錯: $e');
      logger.e('堆疊追蹤: $stackTrace');

      // 確保加載狀態被重置
      setLoading(false);

      // 重新拋出異常，讓調用者處理
      rethrow;
    }
  }

  /// 顯示出生資料選擇對話框
  Future<BirthData?> showPersonSelectionDialog(BuildContext context) async {
    // 使用 FilesPage 的選擇模式來選擇第二人出生資料
    return await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => const FilesPage(
          isSelectionMode: true,
        ),
      ),
    );
  }

  // 格式化角度為度分秒格式。
  String formatDegree(double degree) {
    final int deg = degree.floor();
    final double minDouble = (degree - deg) * 60;
    final int min = minDouble.floor();
    // final int sec = ((minDouble - min) * 60).round();

    // 確保分和秒都是兩位數字
    final String minStr = min.toString().padLeft(2, '0');
    // final String secStr = sec.toString().padLeft(2, '0');

    return '$deg°$minStr\'';
  }

  // 格式化日期時間為指定格式。
  String formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} '
        '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 根據經度獲取星座。
  String getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  // 獲取當前宮位制設定
  Future<HouseSystem> getCurrentHouseSystem() async {
    try {
      final settings = await ChartSettings.loadFromPrefs();
      return settings.houseSystem;
    } catch (e) {
      logger.e('获取宫位制设置失败：$e');
      return HouseSystem.placidus; // 默认使用 Placidus 系统
    }
  }

  /// 生成包含星盤資訊的文本，用於複製到剪貼簿。
  /// 包含行星位置、宮主星、日夜區分、元素統計等信息
  ///
  /// [options] 複製選項，用於控制要包含哪些信息
  Future<String> generateChartInfoText({CopyOptions? options}) async {
    // 如果沒有提供選項，使用默認選項（全選）
    final opts = options ?? CopyOptions.all();
    if (_chartData.firdariaData == null) {
      _firdariaData = await FirdariaService().calculateFirdaria(
        _primaryPerson,
        currentDate: chartData.specificDate,
      );
    }
    chartData.firdariaData = _firdariaData;
    return ChartInfoGenerator.generateChartInfoText(chartData, opts);
  }

  /// 複製星盤資訊
  /// 將星盤資訊複製到剪貼簿。
  ///
  /// [options] 複製選項，用於控制要包含哪些信息
  Future<bool> copyChartInfo({CopyOptions? options}) async {
    if (_chartData.planets == null || _chartData.houses == null) return false;

    setCopying(true);

    try {
      // 生成要複製的文本，傳入用戶選擇的選項
      final String chartInfoText =
          await generateChartInfoText(options: options);

      // 複製到剪貼板
      await FlutterClipboard.copy(chartInfoText);
      return true;
    } catch (e) {
      logger.e('複製失敗: $e');
      return false;
    } finally {
      setCopying(false);
    }
  }

  /// 複製占星諮詢分析資訊
  /// 根據選擇的諮詢主題，複製相關的星盤資訊與分析文案到剪貼簿
  ///
  /// [themeKey] 主題鍵值
  /// [themeInfo] 主題資訊
  Future<bool> copyConsultationAnalysis({
    required String themeKey,
    required dynamic themeInfo,
  }) async {
    if (_chartData.planets == null || _chartData.houses == null) return false;

    setCopying(true);

    try {
      // 生成占星諮詢分析文本
      final String analysisText = await generateConsultationAnalysisText(
        themeKey: themeKey,
        themeInfo: themeInfo,
      );

      // 複製到剪貼板
      await FlutterClipboard.copy(analysisText);
      return true;
    } catch (e) {
      logger.e('複製占星諮詢分析失敗: $e');
      return false;
    } finally {
      setCopying(false);
    }
  }

  /// 複製主題相關的星盤資訊
  /// 根據選擇的占星主題，複製相關的星盤資訊到剪貼簿
  ///
  /// [themeKey] 主題鍵值
  /// [themeInfo] 主題資訊
  /// [options] 複製選項
  Future<bool> copyThemeChartInfo({
    required String themeKey,
    required dynamic themeInfo,
    required CopyOptions options,
  }) async {
    if (_chartData.planets == null || _chartData.houses == null) return false;

    setCopying(true);

    try {
      // 生成主題相關的星盤資訊文本
      final String chartInfoText = await generateThemeChartInfoText(
        themeKey: themeKey,
        themeInfo: themeInfo,
        options: options,
      );

      // 複製到剪貼板
      await FlutterClipboard.copy(chartInfoText);
      return true;
    } catch (e) {
      logger.e('複製主題星盤資訊失敗: $e');
      return false;
    } finally {
      setCopying(false);
    }
  }

  /// 生成主題相關的星盤資訊文本
  /// 根據選擇的占星主題，生成相關的星盤資訊
  Future<String> generateThemeChartInfoText({
    required String themeKey,
    required dynamic themeInfo,
    required CopyOptions options,
  }) async {
    final StringBuffer text = StringBuffer();

    // 主題標題
    text.writeln('=' * 50);
    text.writeln('占星主題分析：${themeInfo.title}');
    text.writeln('分析重點：${themeInfo.keyPoints}');
    text.writeln('=' * 50);
    text.writeln();

    // 主題描述
    text.writeln('【主題說明】');
    text.writeln(themeInfo.description);
    text.writeln();

    // 分析要點
    text.writeln('【分析要點】');
    for (int i = 0; i < themeInfo.keyPoints.length; i++) {
      text.writeln('${i + 1}. ${themeInfo.keyPoints[i]}');
    }
    text.writeln();

    // 生成基本星盤資訊
    final String basicChartInfo = await generateChartInfoText(options: options);
    text.writeln('【星盤資料】');
    text.writeln(basicChartInfo);

    // 根據不同主題添加特殊分析
    text.writeln();
    text.writeln('【主題重點分析】');
    text.writeln(_generateThemeSpecificAnalysis(themeKey, themeInfo));

    return text.toString();
  }

  /// 生成主題特定的分析內容
  String _generateThemeSpecificAnalysis(String themeKey, dynamic themeInfo) {
    final StringBuffer analysis = StringBuffer();

    switch (themeKey) {
      case 'relationship':
        analysis.writeln(_generateRelationshipAnalysis());
        break;
      case 'career':
        analysis.writeln(_generateCareerAnalysis());
        break;
      case 'self_exploration':
        analysis.writeln(_generateSelfExplorationAnalysis());
        break;
      case 'annual_fortune':
        analysis.writeln(_generateAnnualFortuneAnalysis());
        break;
      case 'wealth':
        analysis.writeln(_generateWealthAnalysis());
        break;
      case 'health':
        analysis.writeln(_generateHealthAnalysis());
        break;
      case 'education':
        analysis.writeln(_generateEducationAnalysis());
        break;
      case 'family':
        analysis.writeln(_generateFamilyAnalysis());
        break;
      case 'social':
        analysis.writeln(_generateSocialAnalysis());
        break;
      case 'creativity':
        analysis.writeln(_generateCreativityAnalysis());
        break;
      case 'spirituality':
        analysis.writeln(_generateSpiritualityAnalysis());
        break;
      case 'location':
        analysis.writeln(_generateLocationAnalysis());
        break;
      case 'legal':
        analysis.writeln(_generateLegalAnalysis());
        break;
      default:
        analysis.writeln('此主題的詳細分析功能正在開發中...');
    }

    return analysis.toString();
  }

  /// 生成感情關係分析
  String _generateRelationshipAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【感情關係重點】');
    analysis.writeln('• 第7宮分析：伴侶關係與婚姻狀況');
    analysis.writeln('• 第5宮分析：戀愛模式與浪漫表達');
    analysis.writeln('• 第8宮分析：深度關係與親密連結');
    analysis.writeln('• 金星位置：愛情觀與吸引力特質');
    analysis.writeln('• 火星位置：激情表達與行動模式');
    analysis.writeln('• 月亮位置：情感需求與安全感來源');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 金星與火星的相位關係');
    analysis.writeln('• 第7宮宮主星的位置與相位');
    analysis.writeln('• 月亮與太陽的相位（情感與自我的協調）');
    analysis.writeln('• 互容接納關係（伴侶間的理解與支持）');

    return analysis.toString();
  }

  /// 生成職涯分析
  String _generateCareerAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【職涯發展重點】');
    analysis.writeln('• 第10宮分析：事業方向與社會地位');
    analysis.writeln('• 第6宮分析：工作環境與日常職務');
    analysis.writeln('• 第2宮分析：收入來源與價值觀');
    analysis.writeln('• 中天星座：職業形象與目標');
    analysis.writeln('• 太陽位置：領導能力與核心才能');
    analysis.writeln('• 土星位置：責任感與專業發展');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第10宮宮主星的位置與相位');
    analysis.writeln('• 太陽與土星的相位關係');
    analysis.writeln('• 水星位置（溝通能力）');
    analysis.writeln('• 木星位置（發展機會）');

    return analysis.toString();
  }

  /// 生成自我探索分析
  String _generateSelfExplorationAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【自我探索重點】');
    analysis.writeln('• 第1宮分析：外在表現與人格面具');
    analysis.writeln('• 第12宮分析：潛意識與內在陰影');
    analysis.writeln('• 太陽位置：核心自我與生命目標');
    analysis.writeln('• 月亮位置：內在需求與情感模式');
    analysis.writeln('• 上升點：外在形象與第一印象');
    analysis.writeln('• 元素分布：性格傾向與能量特質');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 太陽與月亮的相位（意識與潛意識的整合）');
    analysis.writeln('• 困難相位（成長挑戰與突破點）');
    analysis.writeln('• 行星尊貴力量（天賦與弱點）');
    analysis.writeln('• 日夜盤特質（能量表達方式）');

    return analysis.toString();
  }

  /// 生成流年運勢分析
  String _generateAnnualFortuneAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【流年運勢重點】');
    analysis.writeln('• 本命盤基礎：個人天賦與挑戰');
    analysis.writeln('• 行星週期：重要行星的運行影響');
    analysis.writeln('• 相位觸發：流年行星與本命行星的互動');
    analysis.writeln('• 宮位激活：不同生活領域的重點時期');
    analysis.writeln('• 時機選擇：重要決策的最佳時間點');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 土星與木星的運行週期');
    analysis.writeln('• 外行星（天王星、海王星、冥王星）的長期影響');
    analysis.writeln('• 日月食對個人星盤的觸發');
    analysis.writeln('• 重要相位的形成與分離時間');

    return analysis.toString();
  }

  /// 生成財富分析
  String _generateWealthAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【財富能量重點】');
    analysis.writeln('• 第2宮分析：個人財富與價值觀');
    analysis.writeln('• 第8宮分析：他人資源與投資理財');
    analysis.writeln('• 第11宮分析：收益來源與財富增長');
    analysis.writeln('• 金星位置：金錢觀與消費模式');
    analysis.writeln('• 木星位置：財富擴展與機會');
    analysis.writeln('• 土星位置：儲蓄能力與財務紀律');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第2宮與第8宮宮主星的相位');
    analysis.writeln('• 金星與木星的相位關係');
    analysis.writeln('• 土星與冥王星的財務影響');
    analysis.writeln('• 財富相關阿拉伯點的位置');

    return analysis.toString();
  }

  /// 生成健康分析
  String _generateHealthAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【健康與體質重點】');
    analysis.writeln('• 第6宮分析：日常健康與疾病傾向');
    analysis.writeln('• 第12宮分析：慢性疾病與隱藏健康問題');
    analysis.writeln('• 第1宮分析：體質特徵與生命力');
    analysis.writeln('• 太陽位置：生命力與整體健康狀況');
    analysis.writeln('• 火星位置：體力與免疫系統');
    analysis.writeln('• 月亮位置：消化系統與情緒健康');
    analysis.writeln('• 土星位置：骨骼系統與慢性問題');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第6宮與第12宮宮主星的位置與相位');
    analysis.writeln('• 太陽與火星的相位關係（生命力與體力）');
    analysis.writeln('• 月亮的困難相位（情緒對健康的影響）');
    analysis.writeln('• 土星的相位（慢性疾病預防）');
    analysis.writeln('• 元素分布與體質傾向分析');

    return analysis.toString();
  }

  /// 生成教育分析
  String _generateEducationAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【學習與教育重點】');
    analysis.writeln('• 第3宮分析：基礎學習能力與溝通技巧');
    analysis.writeln('• 第9宮分析：高等教育與哲學思維');
    analysis.writeln('• 第5宮分析：創意學習與興趣發展');
    analysis.writeln('• 水星位置：思維模式與學習方式');
    analysis.writeln('• 木星位置：智慧發展與學習機會');
    analysis.writeln('• 土星位置：學習紀律與專業深度');
    analysis.writeln('• 天王星位置：創新思維與科技學習');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第3宮與第9宮宮主星的相位關係');
    analysis.writeln('• 水星與木星的相位（學習能力與智慧）');
    analysis.writeln('• 土星與天王星的相位（傳統與創新的平衡）');
    analysis.writeln('• 元素分布對學習風格的影響');
    analysis.writeln('• 適合的學習領域與發展方向');

    return analysis.toString();
  }

  /// 生成家庭分析
  String _generateFamilyAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【家庭與親子重點】');
    analysis.writeln('• 第4宮分析：家庭根基與原生家庭影響');
    analysis.writeln('• 第10宮分析：家庭地位與社會形象');
    analysis.writeln('• 第5宮分析：子女關係與親子互動');
    analysis.writeln('• 第11宮分析：家庭希望與未來規劃');
    analysis.writeln('• 月亮位置：母親關係與情感需求');
    analysis.writeln('• 土星位置：父親關係與家庭責任');
    analysis.writeln('• 太陽位置：家庭中的角色與地位');
    analysis.writeln('• 金星位置：家庭和諧與愛的表達');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第4宮與第10宮的對分軸線分析');
    analysis.writeln('• 月亮與土星的相位（父母關係影響）');
    analysis.writeln('• 第5宮宮主星與子女關係');
    analysis.writeln('• 家庭相關的互容接納關係');
    analysis.writeln('• 家庭傳承與責任的承擔模式');

    return analysis.toString();
  }

  /// 生成社交分析
  String _generateSocialAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【人際關係與社交重點】');
    analysis.writeln('• 第11宮分析：友誼關係與社群參與');
    analysis.writeln('• 第7宮分析：合作關係與夥伴互動');
    analysis.writeln('• 第3宮分析：溝通技巧與日常交流');
    analysis.writeln('• 金星位置：社交魅力與人際吸引力');
    analysis.writeln('• 水星位置：溝通方式與表達技巧');
    analysis.writeln('• 木星位置：人緣發展與社交機會');
    analysis.writeln('• 天秤座相關行星：合作與平衡能力');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第11宮與第7宮宮主星的相位關係');
    analysis.writeln('• 金星與水星的相位（魅力與溝通的結合）');
    analysis.writeln('• 木星的相位（人際機會與發展）');
    analysis.writeln('• 元素分布對社交風格的影響');
    analysis.writeln('• 群體互動模式與領導能力');

    return analysis.toString();
  }

  /// 生成創意分析
  String _generateCreativityAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【創意與藝術重點】');
    analysis.writeln('• 第5宮分析：創意表達與藝術天賦');
    analysis.writeln('• 第12宮分析：靈感來源與潛意識創作');
    analysis.writeln('• 金星位置：美感與藝術品味');
    analysis.writeln('• 海王星位置：想像力與靈感創作');
    analysis.writeln('• 月亮位置：直覺創作與情感表達');
    analysis.writeln('• 天王星位置：創新思維與獨特風格');
    analysis.writeln('• 太陽位置：創作自我與個人風格');
    analysis.writeln('• 水星位置：創意表達技巧');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第5宮宮主星的位置與相位');
    analysis.writeln('• 金星與海王星的相位（美感與想像力）');
    analysis.writeln('• 月亮與天王星的相位（直覺與創新）');
    analysis.writeln('• 太陽與水星的相位（創作自我與表達）');
    analysis.writeln('• 創意相關的元素分布分析');

    return analysis.toString();
  }

  /// 生成靈性分析
  String _generateSpiritualityAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【靈性成長與修行重點】');
    analysis.writeln('• 第9宮分析：哲學思維與宗教傾向');
    analysis.writeln('• 第12宮分析：靈性覺醒與潛意識探索');
    analysis.writeln('• 海王星位置：靈性感知與直覺能力');
    analysis.writeln('• 冥王星位置：深度轉化與重生體驗');
    analysis.writeln('• 木星位置：智慧追求與精神導師');
    analysis.writeln('• 土星位置：修行紀律與靈性結構');
    analysis.writeln('• 月亮交點：業力方向與靈魂使命');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第9宮與第12宮宮主星的相位關係');
    analysis.writeln('• 海王星與冥王星的相位（靈性與轉化）');
    analysis.writeln('• 木星與土星的相位（智慧與紀律的平衡）');
    analysis.writeln('• 月亮交點的位置與相位');
    analysis.writeln('• 靈性成長的阿拉伯點分析');

    return analysis.toString();
  }

  /// 生成占星諮詢分析文本
  /// 根據選擇的諮詢主題，生成完整的分析文本
  Future<String> generateConsultationAnalysisText({
    required String themeKey,
    required dynamic themeInfo,
  }) async {
    final StringBuffer text = StringBuffer();

    // 標題區塊
    // text.writeln('=' * 30);
    // text.writeln('${themeInfo.category}');
    // text.writeln('${themeInfo.title}');
    // text.writeln('=' * 30);
    // text.writeln();

    // 分析說明
    text.writeln('${themeInfo.description}');

    // 分析要點
    for (int i = 0; i < themeInfo.analysisPoints.length; i++) {
      text.writeln('${i + 1}. ${themeInfo.analysisPoints[i]}');
    }
    text.writeln();

    // 分隔線
    text.writeln('-' * 30);
    text.writeln('【星盤基礎資料】');
    text.writeln('-' * 30);

    // 生成基本星盤資訊（使用完整選項）
    const CopyOptions fullOptions = CopyOptions(
      includeBasicInfo: false,
      includePlanetPositions: true,
      includePlanetDignities: true,
      includePlanetSectStatus: true,
      includeHouseRulers: true,
      includeHousePositions: true,
      includeAspects: true,
      includeReceptions: true,
      includeElementStats: true,
      includeArabicPoints: true,
      usePrettyFormat: false,
    );

    final String basicChartInfo =
        await generateChartInfoText(options: fullOptions);
    text.writeln(basicChartInfo);

    // 根據不同主題添加特殊分析指引
    text.writeln();
    text.writeln('-' * 30);
    text.writeln('【分析重點指引】');
    text.writeln('-' * 30);
    text.writeln(_generateConsultationAnalysisGuidance(themeKey, themeInfo));
    text.writeln(
        '請使用口語化輕鬆鼓勵語氣、避免使用術語，適合給初學者或非占星背景者閱讀。\n不要有任何特殊符號或圖示，整份報告都不要出現符號或emoji，方便複製的全不都是純文字格式。');

    return text.toString();
  }

  /// 生成占星諮詢分析指引
  String _generateConsultationAnalysisGuidance(
      String themeKey, dynamic themeInfo) {
    final StringBuffer guidance = StringBuffer();

    switch (themeKey) {
      case 'personality_talent':
        guidance.writeln(_generatePersonalityTalentGuidance());
        break;
      case 'emotional_pattern':
        guidance.writeln(_generateEmotionalPatternGuidance());
        break;
      case 'synastry_interaction':
        guidance.writeln(_generateSynastryInteractionGuidance());
        break;
      case 'career_direction':
        guidance.writeln(_generateCareerDirectionGuidance());
        break;
      case 'financial_tendency':
        guidance.writeln(_generateFinancialTendencyGuidance());
        break;
      case 'progression_transit':
        guidance.writeln(_generateProgressionTransitGuidance());
        break;
      case 'relationship_turning':
        guidance.writeln(_generateRelationshipTurningGuidance());
        break;
      case 'self_exploration':
        guidance.writeln(_generateSelfExplorationGuidance());
        break;
      case 'annual_calendar':
        guidance.writeln(_generateAnnualCalendarGuidance());
        break;
      default:
        guidance.writeln('此主題的詳細分析指引正在開發中...');
    }

    return guidance.toString();
  }

  /// 生成居住環境分析
  String _generateLocationAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【居住與環境重點】');
    analysis.writeln('• 第4宮分析：居住根基與家庭環境需求');
    analysis.writeln('• 第10宮分析：社會環境與職業地點');
    analysis.writeln('• 第9宮分析：遠方遷移與國外居住');
    analysis.writeln('• 第3宮分析：近距離移動與鄰里關係');
    analysis.writeln('• 月亮位置：居住舒適度與情感需求');
    analysis.writeln('• 土星位置：居住穩定性與長期規劃');
    analysis.writeln('• 天王星位置：居住變遷與環境創新');
    analysis.writeln('• 海王星位置：理想居住環境與夢想');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第4宮與第10宮的對分軸線分析');
    analysis.writeln('• 第9宮與第3宮的遷移軸線');
    analysis.writeln('• 月亮與土星的相位（安全感與穩定性）');
    analysis.writeln('• 天王星的相位（環境變化適應能力）');
    analysis.writeln('• 元素分布對環境偏好的影響');

    return analysis.toString();
  }

  /// 生成法律事務分析
  String _generateLegalAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【法律與合約重點】');
    analysis.writeln('• 第7宮分析：合約關係與法律夥伴');
    analysis.writeln('• 第9宮分析：法律事務與司法程序');
    analysis.writeln('• 第8宮分析：糾紛處理與權力鬥爭');
    analysis.writeln('• 第12宮分析：隱藏問題與法律陷阱');
    analysis.writeln('• 土星位置：法律規範與責任承擔');
    analysis.writeln('• 木星位置：正義感與法律保護');
    analysis.writeln('• 水星位置：文件處理與法律溝通');
    analysis.writeln('• 冥王星位置：權力關係與深層糾紛');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第7宮與第9宮宮主星的相位關係');
    analysis.writeln('• 土星與木星的相位（規範與正義的平衡）');
    analysis.writeln('• 水星與冥王星的相位（溝通與權力）');
    analysis.writeln('• 第8宮與第12宮的隱藏風險');
    analysis.writeln('• 法律相關的阿拉伯點分析');

    return analysis.toString();
  }

  /// 生成性格天賦分析指引
  String _generatePersonalityTalentGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【分析重點】');
    guidance.writeln('• 三大要素組合：觀察太陽、月亮、上升的星座組合，分析外在表現與內在需求的協調性');
    guidance.writeln('• 元素統計：火土風水的分布比例，判斷性格的主導能量類型');
    guidance.writeln('• 三分性統計：基本、固定、變動的分布，了解行動模式與適應能力');
    guidance.writeln('• 命主星：上升星座的守護星位置，代表人生主要發展方向');
    guidance.writeln('• 個人行星：金星（價值觀）、水星（思維）、火星（行動力）的表現方式');
    guidance.writeln();
    guidance.writeln('【建議分析步驟】');
    guidance.writeln('1. 先分析三大要素的和諧度與衝突點');
    guidance.writeln('2. 觀察元素與三分性的優勢與不足');
    guidance.writeln('3. 找出命主星的位置與相位，確定人生主軸');
    guidance.writeln('4. 分析個人行星的表達方式與天賦潛能');
    guidance.writeln('5. 整合以上資訊，提供性格特質與發展建議');

    return guidance.toString();
  }

  /// 生成情感模式分析指引
  String _generateEmotionalPatternGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【分析重點】');
    guidance.writeln('• 金星與火星：愛情觀與激情表達，觀察兩者的星座、宮位與相位關係');
    guidance.writeln('• 月亮與IC：情感需求與安全感來源，分析內在的情感模式');
    guidance.writeln('• 第五宮：戀愛模式與浪漫表達，宮主星與宮內行星的影響');
    guidance.writeln('• 第七宮：伴侶關係與婚姻模式，長期關係的期待與挑戰');
    guidance.writeln('• 土星影響：關係中的責任與限制，成熟度與承諾能力');
    guidance.writeln('• 凱龍星：情感創傷與療癒，關係中的敏感點與成長機會');
    guidance.writeln('• 南北交點：業力關係模式，需要學習的關係課題');
    guidance.writeln();
    guidance.writeln('【建議分析步驟】');
    guidance.writeln('1. 分析金星與火星的配置，了解愛情觀與吸引力模式');
    guidance.writeln('2. 觀察月亮與IC的位置，理解情感需求與安全感來源');
    guidance.writeln('3. 檢視第五、七宮的配置，分析戀愛與婚姻傾向');
    guidance.writeln('4. 考慮土星、凱龍星的影響，找出關係挑戰與成長點');
    guidance.writeln('5. 整合南北交點的訊息，提供關係發展建議');

    return guidance.toString();
  }

  /// 生成合盤互動分析指引
  String _generateSynastryInteractionGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【分析重點】');
    guidance.writeln('• 太陽月亮相位：基本相容性與情感連結，觀察和諧與衝突面向');
    guidance.writeln('• 金星火星互動：愛情吸引力與性吸引力，浪漫與激情的化學反應');
    guidance.writeln('• 宮位落點：內行星落入對方重要宮位（1、5、7宮）的影響');
    guidance.writeln('• 相位模式：合相（融合）、三分六分（和諧）、四分對分（挑戰）');
    guidance.writeln('• 元素相容：火土風水元素的互補與衝突');
    guidance.writeln('• 複合中點：兩人星盤的中點分析，關係的核心主題');
    guidance.writeln();
    guidance.writeln('【建議分析步驟】');
    guidance.writeln('1. 比較兩人的太陽、月亮、上升，評估基本相容性');
    guidance.writeln('2. 分析金星、火星的互動，了解愛情與激情的化學反應');
    guidance.writeln('3. 觀察內行星落入對方宮位的影響，特別是1、5、7宮');
    guidance.writeln('4. 檢視主要相位模式，找出和諧與挑戰的平衡點');
    guidance.writeln('5. 提供關係發展建議與相處之道');

    return guidance.toString();
  }

  /// 生成職涯方向分析指引
  String _generateCareerDirectionGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【分析重點】');
    guidance.writeln('• 中天（MC）：職業形象與社會地位，代表個人在社會中的角色定位');
    guidance.writeln('• 第十宮：事業發展方向，宮主星與宮內行星的職業指引');
    guidance.writeln('• 太陽位置：核心才能與領導風格，個人在工作中的發光點');
    guidance.writeln('• 火星位置：行動力與競爭力，工作中的動力來源與執行方式');
    guidance.writeln('• 水星位置：溝通技巧與思維模式，適合的工作類型與學習能力');
    guidance.writeln('• 行星集中：多行星集中的宮位，代表主要的人生發展領域');
    guidance.writeln('• 第二宮：收入來源與價值觀，金錢觀對職業選擇的影響');
    guidance.writeln('• 第六宮：工作環境與日常職務，適合的工作節奏與團隊合作');
    guidance.writeln();
    guidance.writeln('【建議分析步驟】');
    guidance.writeln('1. 分析中天星座與第十宮配置，確定職業方向與社會角色');
    guidance.writeln('2. 觀察太陽、火星、水星的位置，了解工作風格與能力特質');
    guidance.writeln('3. 檢視行星集中區域，找出主要的人生發展主題');
    guidance.writeln('4. 考慮第二、六宮的影響，分析收入模式與工作環境偏好');
    guidance.writeln('5. 整合以上資訊，提供職涯發展建議與轉職時機');

    return guidance.toString();
  }

  /// 生成財務傾向分析指引
  String _generateFinancialTendencyGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【分析重點】');
    guidance.writeln('• 第二宮：個人財富與價值觀，賺錢能力與金錢觀念');
    guidance.writeln('• 第八宮：他人資源與投資理財，合夥事業與深度財務管理');
    guidance.writeln('• 金星位置：消費模式與美感價值，對物質享受的需求與品味');
    guidance.writeln('• 木星位置：財富擴展與機會，幸運的財務領域與投資方向');
    guidance.writeln('• 土星位置：儲蓄紀律與財務責任，長期財務規劃與風險控制');
    guidance.writeln('• 固定星座：金牛、獅子、天蠍、水瓶座行星的財務影響');
    guidance.writeln('• 相位關係：金錢相關行星間的和諧與衝突');
    guidance.writeln();
    guidance.writeln('【建議分析步驟】');
    guidance.writeln('1. 分析第二、八宮的配置，了解財富來源與管理模式');
    guidance.writeln('2. 觀察金星、木星的位置，評估消費習慣與財富機會');
    guidance.writeln('3. 檢視土星的影響，分析儲蓄能力與財務紀律');
    guidance.writeln('4. 考慮固定星座的強勢，判斷財務穩定性與累積能力');
    guidance.writeln('5. 整合相位關係，提供理財建議與投資方向');

    return guidance.toString();
  }

  /// 生成推運流年分析指引
  String _generateProgressionTransitGuidance() {
    final StringBuffer guidance = StringBuffer();

    // guidance.writeln('【分析重點】');
    // guidance.writeln('• 外行星流年：土星、天王星、海王星、冥王星與本命行星的相位');
    // guidance.writeln('• 木星週期：約12年一輪的擴展機會，每年經過的宮位主題');
    // guidance.writeln('• 土星週期：約29年一輪的責任課題，重要的人生轉折點');
    // guidance.writeln('• 次限推運：次限太陽、月亮的移動，內在成長與情感變化');
    // guidance.writeln('• 新月觸發：新月落入的宮位，該月份的重點發展領域');
    // guidance.writeln('• 相位時間：精確相位的形成與分離時間，影響的強度與持續期');
    // guidance.writeln();
    // guidance.writeln('【建議分析步驟】');
    // guidance.writeln('1. 找出主要挑戰與機會');
    // guidance.writeln('2. 分析幸運領域');
    // guidance.writeln('3. 觀察需要承擔的責任與學習的課題');
    // guidance.writeln('4. 加入次限推運的訊息，分析內在成長的節奏');
    // guidance.writeln('5. 整理為時間軸格式，提供具體的行動建議');

    return guidance.toString();
  }

  /// 生成關係轉折點分析指引
  String _generateRelationshipTurningGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【分析重點】');
    guidance.writeln('• 流年金星：愛情機會與關係和諧，經過重要宮位的時間點');
    guidance.writeln('• 流年木星：關係擴展與婚姻機會，與本命金星、第七宮的互動');
    guidance.writeln('• 推運金星：內在愛情觀的變化，對關係期待的轉變');
    guidance.writeln('• 推運月亮：情感需求的週期變化，約2.5年的情感主題');
    guidance.writeln('• 推運太陽：自我認同的發展，影響關係中的角色定位');
    guidance.writeln('• 合盤觸發：流年行星與對方本命行星的相位時間');
    guidance.writeln('• 日月食影響：觸發關係軸線（第一/七宮、第五/十一宮）的時間');
    guidance.writeln();
    guidance.writeln('【建議分析步驟】');
    guidance.writeln('1. 追蹤流年金星、木星的移動軌跡，找出關係機會期');
    guidance.writeln('2. 分析推運行星的變化，了解內在關係需求的轉變');
    guidance.writeln('3. 檢視合盤中的觸發時間，預測關係發展的關鍵時刻');
    guidance.writeln('4. 考慮日月食的影響，找出關係轉折的重要節點');
    guidance.writeln('5. 整理為時間表格式，提供具體的關係發展建議');

    return guidance.toString();
  }

  /// 生成自我探索對話指引
  String _generateSelfExplorationGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【對話重點】');
    guidance.writeln('• 人生主題：根據太陽、月亮、上升的配置，找出當前的人生課題');
    guidance.writeln('• 內在衝突：觀察困難相位與行星衝突，了解內在的矛盾與掙扎');
    guidance.writeln('• 突破方向：利用和諧相位與行星優勢，找出成長的可能性');
    guidance.writeln('• 行動建議：結合當前流年與推運，提供具體可行的行動方案');
    guidance.writeln('• 鼓勵引導：使用正向語言，幫助當事人看見自己的潛能與價值');
    guidance.writeln();
    guidance.writeln('【對話技巧】');
    guidance.writeln('• 使用開放式問題，引導當事人自我反思');
    guidance.writeln('• 將占星語言轉化為生活化的表達方式');
    guidance.writeln('• 強調選擇權與主動性，避免宿命論的表達');
    guidance.writeln('• 提供具體的行動建議，而非抽象的概念');
    guidance.writeln('• 保持同理心與支持性的語調');
    guidance.writeln();
    guidance.writeln('【建議對話流程】');
    guidance.writeln('1. 先了解當事人目前面臨的主要困擾或疑問');
    guidance.writeln('2. 根據星盤配置，反映其內在的特質與需求');
    guidance.writeln('3. 指出可能的內在衝突，並給予理解與接納');
    guidance.writeln('4. 提供1-2個具體的行動建議或思考方向');
    guidance.writeln('5. 鼓勵當事人相信自己的能力與選擇');

    return guidance.toString();
  }

  /// 生成年度星象日曆指引
  String _generateAnnualCalendarGuidance() {
    final StringBuffer guidance = StringBuffer();

    guidance.writeln('【日曆重點】');
    guidance.writeln('• 金錢主題：流年木星、土星經過第二、八宮的時間，財務機會與挑戰');
    guidance.writeln('• 感情主題：流年金星、木星觸發第五、七宮的時間，愛情與關係發展');
    guidance.writeln('• 工作主題：流年行星經過第六、十宮的時間，職涯變化與機會');
    guidance.writeln('• 情緒主題：新月、滿月觸發重要宮位的時間，情感波動與釋放');
    guidance.writeln('• 療癒主題：流年凱龍星、海王星的相位時間，心靈成長與療癒機會');
    guidance.writeln('• 轉變主題：流年天王星、冥王星的相位時間，重大變化與轉型');
    guidance.writeln();
    guidance.writeln('【日曆格式】');
    guidance.writeln('每筆記錄包含：');
    guidance.writeln('• 日期：具體的年月日');
    guidance.writeln('• 主題：金錢/感情/工作/情緒/療癒/轉變');
    guidance.writeln('• 觸發原因：流年行星與本命行星/宮位的相位');
    guidance.writeln('• 建議行動：該時期適合進行的具體行動');
    guidance.writeln();
    guidance.writeln('【建議製作步驟】');
    guidance.writeln('1. 計算流年行星的移動軌跡，找出重要的相位時間');
    guidance.writeln('2. 分析新月、滿月的觸發點，標記情感波動期');
    guidance.writeln('3. 整理為月份分類，每月列出重點星象事件');
    guidance.writeln('4. 為每個事件提供具體的行動建議');
    guidance.writeln('5. 製作成易於查閱的日曆格式');

    return guidance.toString();
  }

  /// 生成包含星盤資訊的 PDF 報告。
  ///
  /// 返回值：PDF 文件的字節數據，如果生成失敗則返回 null
  /// 如果生成過程中發生錯誤，會拋出異常並包含錯誤信息
  Future<Uint8List?> generatePdf() async {
    if (_chartData.planets == null || _chartData.houses == null) {
      logger.w('無法生成 PDF：行星或宮位數據為空');
      return null;
    }

    setGeneratingPdf(true);

    try {
      // 檢查數據是否完整
      if (_chartData.aspects == null || _chartData.aspects!.isEmpty) {
        logger.w('相位數據為空，但仍然繼續生成 PDF');
      }

      // 記錄開始生成 PDF 的時間
      final startTime = DateTime.now();
      logger.i('開始生成 PDF');

      // 生成 PDF
      final pdfBytes = await ChartPdfGenerator.generatePdf(
        birthData: _primaryPerson,
        planets: _chartData.planets!,
        aspects: _chartData.aspects ?? [],
        housesData: _chartData.houses!,
        latitude: _latitude,
        longitude: _longitude,
      );

      // 計算生成時間
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      logger.i(
          'PDF 生成成功，大小: ${pdfBytes.length} 字節，耗時: ${duration.inMilliseconds} 毫秒');
      return pdfBytes;
    } catch (e, stackTrace) {
      logger.e('生成 PDF 失敗: $e');
      logger.e('堆疊追蹤: $stackTrace');

      // 將錯誤信息向上拋出，以便在 UI 中顯示
      if (e.toString().contains('TooManyPagesException')) {
        throw Exception('生成的 PDF 頁數超出限制，請減少內容或聯繫開發者。');
      } else if (e.toString().contains('OutOfMemoryError') ||
          e.toString().contains('memory')) {
        throw Exception('生成 PDF 時記憶體不足，請重新啟動應用或聯繫開發者。');
      } else if (e.toString().contains('timeout') ||
          e.toString().contains('timed out')) {
        throw Exception('生成 PDF 時逾時，請重試或聯繫開發者。');
      }
      throw Exception('生成 PDF 時發生錯誤: ${e.toString()}');
    } finally {
      setGeneratingPdf(false);
    }
  }

  // 發送包含星盤數據的電子郵件。
  Future<bool> sendEmail({required String email, required bool useHtml}) async {
    setSendingEmail(true);

    try {
      final bool success = await ChartEmailSender.sendChartData(
        birthData: _primaryPerson,
        planets: _chartData.planets!,
        aspects: _chartData.aspects!,
        housesData: _chartData.houses!,
        recipientEmail: email,
        useHtml: useHtml,
        latitude: _latitude,
        longitude: _longitude,
      );

      return success;
    } catch (e) {
      logger.e('發送失敗: $e');
      return false;
    } finally {
      setSendingEmail(false);
    }
  }


  // 獲取宮位顏色
  // 根據宮位元素屬性獲取顏色。
  Color getHouseColor(int houseNumber) {
    // 根據宮位的元素屬性分配顏色
    switch (houseNumber % 4) {
      case 1: // 火象宮 (1, 5, 9)
        return Colors.red[700]!;
      case 2: // 土象宮 (2, 6, 10)
        return Colors.brown[600]!;
      case 3: // 風象宮 (3, 7, 11)
        return Colors.blue[600]!;
      case 0: // 水象宮 (4, 8, 12)
        return Colors.teal[600]!;
      default:
        return Colors.grey[700]!;
    }
  }

  /// 根據相位類型返回對應的顏色
  Color getAspectColor(String aspectType) {
    switch (aspectType) {
      case '合相':
        return const Color(0xFF224EA5); // 藍色
      case '六分相':
        return const Color(0xFF2999A4); // 淺藍色
      case '四分相':
        return Colors.red; // 紅色
      case '三分相':
        return Colors.green; // 綠色
      case '對分相':
        return const Color(0xFF051883); // 深藍色
      case '接納':
        return Colors.purple; // 紫色
      default:
        return Colors.grey; // 灰色
    }
  }

  /// 計算界主星配置法的完整時間表
  ///
  /// 根據出生資料計算界主星配置法的時間表
  Future<TermRulerTimelineResult>
      calculateTermRulerProgressionTimeline() async {
    try {
      // 獲取出生資料
      DateTime birthDateTime =
          _chartData.specificDate ?? _chartData.primaryPerson.dateTime;
      if (_chartData.chartType == ChartType.solarReturn &&
          _chartData.returnDate != null) {
        birthDateTime = chartData.returnDate!;
      }

      final latitude = _chartData.primaryPerson.latitude;
      final longitude = _chartData.primaryPerson.longitude;

      // 計算宮位數據以獲取上升點
      if (_chartData.houses == null || _chartData.houses!.ascmc.isEmpty) {
        throw Exception('無法獲取宮位數據');
      }

      final double ascendantLongitude = _chartData.houses!.ascmc[0];
      final bool isNorth = latitude >= 0;

      // 計算界主星配置法
      final TermRulerProgressionResult termRulerProgression = _astrologyService
          .calculateTermRulerProgression(ascendantLongitude, latitude, isNorth);

      // 計算完整的時間表
      final List<TermRulerTimelineItem> timeline =
          calculateFullTermRulerTimeline(
              ascendantLongitude, latitude, isNorth, birthDateTime);

      return TermRulerTimelineResult(
        currentInfo: termRulerProgression,
        timeline: timeline,
        birthDateTime: birthDateTime,
        ascendantLongitude: ascendantLongitude,
        latitude: latitude,
        longitude: longitude,
      );
    } catch (e) {
      logger.e('計算界主星配置法時間表失敗: $e');
      rethrow;
    }
  }

  /// 計算小限法的完整時間表
  ///
  /// 根據出生資料計算小限法的時間表
  Future<ProfectionTimelineResult> calculateProfectionTimeline() async {
    try {
      // 確定要使用的當前日期：如果有推運時間則使用推運時間，否則使用現在時間
      final currentDate = _specificDate ?? DateTime.now();
      logger.i('小限法時間表計算使用的當前日期: $currentDate');

      // 使用 ProfectionService 計算小限法時間表
      final profectionService = ProfectionService();
      final result = await profectionService.calculateProfectionTimeline(
        _primaryPerson,
        currentDate: currentDate,
        yearsRange: 15, // 計算前後各15年
        housesData: _chartData.houses, // 傳遞本命盤宮位數據
      );

      logger.i('小限法時間表計算完成，共 ${result.timeline.length} 年');
      return result;
    } catch (e) {
      logger.e('計算小限法時間表失敗: $e');
      rethrow;
    }
  }

  /// 計算完整的界主星配置法時間表（逆時針方向 - 度數遞增，完整圈）
  List<TermRulerTimelineItem> calculateFullTermRulerTimeline(
      double ascendantLongitude,
      double latitude,
      bool isNorth,
      DateTime birthDateTime) {
    final List<TermRulerTimelineItem> timeline = [];
    DateTime currentDateTime = birthDateTime;

    // 計算完整的360度循環，支援多圈計算
    double currentLongitude = ascendantLongitude;
    final double startLongitude = ascendantLongitude;
    bool isFirstTerm = true;

    // 計算目標度數：支援五圈（5 × 360 = 1800度）
    const int maxCircles = 1;
    final double targetDegrees = startLongitude + (360.0 * maxCircles);

    // 計算完整圈（360度），若超過360度繼續往下計算，多處理五圈1800度
    while (currentLongitude < targetDegrees) {
      // 計算當前圈數和圈內度數
      final double totalDegreesFromStart = currentLongitude - startLongitude;
      final int currentCircle = (totalDegreesFromStart / 360.0).floor() + 1;
      final double degreesInCurrentCircle = totalDegreesFromStart % 360.0;

      final String currentSign = getZodiacSign(currentLongitude);
      final double degreeInSign = currentLongitude % 30.0;

      // 獲取該星座的界主星定義
      final Map<String, int>? terms =
          AstrologyConstants.ZODIAC_TERMS[currentSign];
      if (terms == null) {
        currentLongitude += 1.0; // 跳過無效星座
        continue;
      }

      // 獲取時間數據
      final List<double> timeData = AscensionTable.getTimeByLatitudeAndSign(
          latitude, currentSign, isNorth);
      final double timePerDegree = timeData[1]; // 每度所需時間（天）

      // 將字符串key轉換為double並正序排列（逆時針 = 度數遞增）
      final List<int> degrees = terms.keys.map((key) => int.parse(key)).toList()
        ..sort(); // 正序排列：0, 6, 12, 20, 25

      // 找到當前度數所在的界
      int? currentTermStart;
      double? nextTermStart;
      int? currentTermRuler;

      for (int i = 0; i < degrees.length; i++) {
        if (degreeInSign >= degrees[i]) {
          currentTermStart = degrees[i];
          currentTermRuler = terms[degrees[i].toString()];
          // 找到下一個界的起始度數
          if (i < degrees.length - 1) {
            nextTermStart = degrees[i + 1].toDouble();
          } else {
            nextTermStart = 30; // 星座結束
          }
        }
      }

      if (currentTermStart != null &&
          nextTermStart != null &&
          currentTermRuler != null) {
        // 計算這個界的剩餘度數
        final double remainingDegrees = nextTermStart - degreeInSign;
        final double remainingDays = remainingDegrees * timePerDegree;

        // 使用精確的分鐘計算
        final remainingMinutes = remainingDays * 24 * 60;
        final endDateTime =
            currentDateTime.add(Duration(minutes: remainingMinutes.round()));

        // 計算星座總時間（檢查是否為星座切換）
        double? signTotalTime;
        bool isSignChange = false;

        // 檢查是否為星座的第一個界或接近星座邊界
        if (degreeInSign <= 1.0 || (currentLongitude % 30.0) <= 1.0) {
          // 獲取這個星座的總時間
          final List<double> signTimeData =
              AscensionTable.getTimeByLatitudeAndSign(
                  latitude, currentSign, isNorth);
          signTotalTime = signTimeData[0]; // 走完整個星座所需時間（天）
          isSignChange = true;
        }

        timeline.add(TermRulerTimelineItem(
          termRuler: currentTermRuler,
          termRulerName: _getPlanetNameById(currentTermRuler),
          timePerDegree: timePerDegree,
          startDegree: degreeInSign,
          endDegree: nextTermStart,
          startDateTime: currentDateTime,
          endDateTime: endDateTime,
          durationDays: remainingDays,
          isCurrentTerm: isFirstTerm,
          sign: currentSign,
          direction: '逆時針',
          longitude: currentLongitude,
          signTotalTime: signTotalTime,
          // 星座總時間
          isSignChange: isSignChange,
          // 是否為星座切換
          circleNumber: currentCircle,
          // 當前圈數
          degreesInCircle: degreesInCurrentCircle, // 圈內累計度數
        ));

        currentDateTime = endDateTime;
        currentLongitude += remainingDegrees;
        isFirstTerm = false;
      } else {
        // 如果無法找到界，跳到下一度
        currentLongitude += 1.0;
      }

      // 防止無限循環，增加限制以支援五圈計算
      if (timeline.length > 500) break; // 增加到500個項目以支援五圈計算
    }

    return timeline;
  }

  /// 根據行星ID獲取行星名稱
  String _getPlanetNameById(int planetId) {
    for (final planet in AstrologyConstants.PLANETS) {
      if (planet['id'] == planetId) {
        return planet['name'] as String;
      }
    }
    return '未知行星';
  }

  // ==================== 主題分析方法 ====================

  /// 生成主題相關的星盤資訊文本
  ///
  /// 根據指定的主題類型分析星盤，生成相應的文本內容
  Future<ChartThemeAnalysisResult> generateThemeAnalysis(
    ChartThemeType themeType, {
    ConsultationStyle? consultationStyle,
  }) async {
    switch (themeType) {
      case ChartThemeType.coreStructure:
        return await _analyzeCoreStructure();
      case ChartThemeType.emotionalRelationships:
        return await _analyzeEmotionalRelationships();
      case ChartThemeType.careerAndMission:
        return await _analyzeCareerAndMission();
      case ChartThemeType.psychologicalHealing:
        return await _analyzePsychologicalHealing();
      case ChartThemeType.transitionalAnalysis:
        return await _analyzeTransitionalPhases();
      case ChartThemeType.decisionMaking:
        return await _analyzeDecisionMaking();
      case ChartThemeType.consultationStyles:
        return await _analyzeConsultationStyles(consultationStyle);
    }
  }

  /// 一、命盤核心架構（性格與天賦）分析
  Future<ChartThemeAnalysisResult> _analyzeCoreStructure() async {
    final sections = <ThemeAnalysisSection>[];
    final keyElements = <String>[];
    final recommendations = <String>[];

    // 創建主題分析服務
    final analysisService = ChartThemeAnalysisService(_chartData);

    // 太陽、月亮、上升三大要素解析
    final sunMoonAscSection = await analysisService.analyzeSunMoonAscendant();
    sections.add(sunMoonAscSection);

    // 宮主星的落點與相位（天賦分布與表現方式）
    final houseRulersSection = await analysisService.analyzeHouseRulers();
    sections.add(houseRulersSection);

    // 重點合相分析
    final conjunctionsSection = await analysisService.analyzeKeyConjunctions();
    sections.add(conjunctionsSection);

    // 四元素比例（火土風水）、三分性（基變固）
    final elementsSection = await analysisService.analyzeElementsAndQualities();
    sections.add(elementsSection);

    // 行星集群分析
    final stelliumSection = await analysisService.analyzePlanetaryStelliums();
    sections.add(stelliumSection);

    // 提取關鍵要素
    keyElements.addAll(_extractCoreStructureKeyElements());

    // 生成建議
    recommendations.addAll(_generateCoreStructureRecommendations());

    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.coreStructure,
      title: '命盤核心架構（性格與天賦）',
      summary: _generateCoreStructureSummary(),
      sections: sections,
      keyElements: keyElements,
      recommendations: recommendations,
      analysisTime: DateTime.now(),
    );
  }

  /// 二、情感與親密關係分析
  Future<ChartThemeAnalysisResult> _analyzeEmotionalRelationships() async {
    final sections = <ThemeAnalysisSection>[];
    final keyElements = <String>[];
    final recommendations = <String>[];

    // 金星與火星的位置與相位（吸引力 vs 行動力）
    final venusMarsSectionn = await _analyzeVenusAndMars();
    sections.add(venusMarsSectionn);

    // 第五宮與第七宮的主題與宮主星
    final relationshipHousesSection = await _analyzeRelationshipHouses();
    sections.add(relationshipHousesSection);

    // 月亮的相位與早年情感經驗
    final moonAspectsSection = await _analyzeMoonAspects();
    sections.add(moonAspectsSection);

    // 婚神星、合婚盤、中點盤分析
    final synastrySectionn = await _analyzeSynastryElements();
    sections.add(synastrySectionn);

    // 月亮交點、凱龍星與阿拉伯點的影響
    final nodesChironSection = await _analyzeNodesAndChiron();
    sections.add(nodesChironSection);

    // 提取關鍵要素
    keyElements.addAll(_extractEmotionalKeyElements());

    // 生成建議
    recommendations.addAll(_generateEmotionalRecommendations());

    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.emotionalRelationships,
      title: '情感與親密關係',
      summary: _generateEmotionalSummary(),
      sections: sections,
      keyElements: keyElements,
      recommendations: recommendations,
      analysisTime: DateTime.now(),
    );
  }

  /// 三、職涯方向與人生使命分析
  Future<ChartThemeAnalysisResult> _analyzeCareerAndMission() async {
    final sections = <ThemeAnalysisSection>[];
    final keyElements = <String>[];
    final recommendations = <String>[];

    // 中天（MC）、第十宮的星與宮主星
    final midheavenSection = await _analyzeMidheaven();
    sections.add(midheavenSection);

    // 第二、六、十宮的整體配置
    final careerHousesSection = await _analyzeCareerHouses();
    sections.add(careerHousesSection);

    // 北交點與業力軌跡（南北交點軸）
    final nodesAxisSection = await _analyzeNodesAxis();
    sections.add(nodesAxisSection);

    // 宮主星連結與星座定位
    final houseRulerConnectionsSection = await _analyzeHouseRulerConnections();
    sections.add(houseRulerConnectionsSection);

    // 法達主星、推運、流年與重大週期的交會
    final timingSection = await _analyzeCareerTiming();
    sections.add(timingSection);

    // 提取關鍵要素
    keyElements.addAll(_extractCareerKeyElements());

    // 生成建議
    recommendations.addAll(_generateCareerRecommendations());

    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.careerAndMission,
      title: '職涯方向與人生使命',
      summary: _generateCareerSummary(),
      sections: sections,
      keyElements: keyElements,
      recommendations: recommendations,
      analysisTime: DateTime.now(),
    );
  }

  /// 四、心理療癒與潛意識模式分析
  Future<ChartThemeAnalysisResult> _analyzePsychologicalHealing() async {
    final sections = <ThemeAnalysisSection>[];
    final keyElements = <String>[];
    final recommendations = <String>[];

    // 月亮、冥王星、凱龍星的相位
    final healingPlanetsSection = await _analyzeHealingPlanets();
    sections.add(healingPlanetsSection);

    // 第四宮、第十二宮的星體與結構
    final unconsciousHousesSection = await _analyzeUnconsciousHouses();
    sections.add(unconsciousHousesSection);

    // 海王星與土星的關聯（逃避 vs 界線）
    final neptuneSaturnSection = await _analyzeNeptuneSaturn();
    sections.add(neptuneSaturnSection);

    // 阿拉伯點分析
    final arabicPointsSection = await _analyzeArabicPoints();
    sections.add(arabicPointsSection);

    // 黑月莉莉絲與內在陰影
    final lilithSection = await _analyzeLilith();
    sections.add(lilithSection);

    // 提取關鍵要素
    keyElements.addAll(_extractHealingKeyElements());

    // 生成建議
    recommendations.addAll(_generateHealingRecommendations());

    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.psychologicalHealing,
      title: '心理療癒與潛意識模式',
      summary: _generateHealingSummary(),
      sections: sections,
      keyElements: keyElements,
      recommendations: recommendations,
      analysisTime: DateTime.now(),
    );
  }

  /// 五、流年與推運階段分析
  Future<ChartThemeAnalysisResult> _analyzeTransitionalPhases() async {
    final sections = <ThemeAnalysisSection>[];
    final keyElements = <String>[];
    final recommendations = <String>[];

    // 流年（Transits）：與本命星形成的主要相位
    final transitsSection = await _analyzeTransits();
    sections.add(transitsSection);

    // 次限（Secondary Progressions）：情緒與心靈狀態的內在變化
    final progressionsSection = await _analyzeProgressions();
    sections.add(progressionsSection);

    // 法達主星系統（Firdaria）：人生長期主題的切換
    final firdariaSection = await _analyzeFirdariaSystem();
    sections.add(firdariaSection);

    // 太陽弧推運（Solar Arc）分析重點事件
    final solarArcSection = await _analyzeSolarArc();
    sections.add(solarArcSection);

    // 合盤流年（流年與中點盤）
    final compositeTransitsSection = await _analyzeCompositeTransits();
    sections.add(compositeTransitsSection);

    // 提取關鍵要素
    keyElements.addAll(_extractTransitionalKeyElements());

    // 生成建議
    recommendations.addAll(_generateTransitionalRecommendations());

    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.transitionalAnalysis,
      title: '流年與推運階段分析',
      summary: _generateTransitionalSummary(),
      sections: sections,
      keyElements: keyElements,
      recommendations: recommendations,
      analysisTime: DateTime.now(),
    );
  }

  /// 六、決策與問題討論分析
  Future<ChartThemeAnalysisResult> _analyzeDecisionMaking() async {
    final sections = <ThemeAnalysisSection>[];
    final keyElements = <String>[];
    final recommendations = <String>[];

    // 擇日占星（Electional Astrology）：挑選適合時機
    final electionalSection = await _analyzeElectionalAstrology();
    sections.add(electionalSection);

    // 宮位主題與流年星體進駐分析
    final houseTransitsSection = await _analyzeHouseTransits();
    sections.add(houseTransitsSection);

    // 行運觸發角度與重點星體相位變化
    final transitTriggersSection = await _analyzeTransitTriggers();
    sections.add(transitTriggersSection);

    // 宿命 vs 自由意志的討論框架
    final fateVsFreeWillSection = await _analyzeFateVsFreeWill();
    sections.add(fateVsFreeWillSection);

    // 星盤問卜（如月亮宮位與行運）進行短期指引
    final horarySection = await _analyzeHoraryGuidance();
    sections.add(horarySection);

    // 提取關鍵要素
    keyElements.addAll(_extractDecisionKeyElements());

    // 生成建議
    recommendations.addAll(_generateDecisionRecommendations());

    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.decisionMaking,
      title: '決策與問題討論',
      summary: _generateDecisionSummary(),
      sections: sections,
      keyElements: keyElements,
      recommendations: recommendations,
      analysisTime: DateTime.now(),
    );
  }

  /// 七、占星諮詢的進階風格差異分析
  Future<ChartThemeAnalysisResult> _analyzeConsultationStyles(
    ConsultationStyle? style,
  ) async {
    final sections = <ThemeAnalysisSection>[];
    final keyElements = <String>[];
    final recommendations = <String>[];

    if (style != null) {
      // 根據指定風格進行分析
      final styleSection = await _analyzeSpecificStyle(style);
      sections.add(styleSection);
    } else {
      // 分析所有風格的差異
      for (final consultationStyle in ConsultationStyle.values) {
        final styleSection = await _analyzeSpecificStyle(consultationStyle);
        sections.add(styleSection);
      }
    }

    // 風格比較分析
    final comparisonSection = await _analyzeStyleComparison();
    sections.add(comparisonSection);

    // 提取關鍵要素
    keyElements.addAll(_extractStyleKeyElements(style));

    // 生成建議
    recommendations.addAll(_generateStyleRecommendations(style));

    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.consultationStyles,
      title: '占星諮詢的進階風格差異',
      summary: _generateStyleSummary(style),
      sections: sections,
      keyElements: keyElements,
      recommendations: recommendations,
      analysisTime: DateTime.now(),
    );
  }

  // ==================== 輔助方法實現 ====================

  /// 提取命盤核心架構的關鍵要素
  List<String> _extractCoreStructureKeyElements() {
    final keyElements = <String>[];

    if (_chartData.planets != null) {
      // 添加太陽、月亮、上升的基本資訊
      final sunList = _chartData.planets!.where((p) => p.name == '太陽').toList();
      final moonList =
          _chartData.planets!.where((p) => p.name == '月亮').toList();

      if (sunList.isNotEmpty) {
        final sun = sunList.first;
        keyElements.add('太陽${sun.sign}座第${sun.house}宮');
      }
      if (moonList.isNotEmpty) {
        final moon = moonList.first;
        keyElements.add('月亮${moon.sign}座第${moon.house}宮');
      }
    }

    return keyElements;
  }

  /// 生成命盤核心架構的建議
  List<String> _generateCoreStructureRecommendations() {
    return [
      '深入了解太陽、月亮、上升三大要素的整合方式',
      '觀察宮主星的連結，了解天賦的表現管道',
      '注意重點合相帶來的強化能量',
      '平衡四元素的發展，避免過度偏重某一元素',
    ];
  }

  /// 生成命盤核心架構的摘要
  String _generateCoreStructureSummary() {
    return '命盤核心架構分析著重於建立基本的自我理解，透過太陽、月亮、上升三大要素，'
        '結合宮主星落點、重點合相、四元素分布等，幫助了解個人的性格特質與天賦潛能。';
  }

  /// 提取情感關係的關鍵要素
  List<String> _extractEmotionalKeyElements() {
    final keyElements = <String>[];

    if (_chartData.planets != null) {
      final venusList =
          _chartData.planets!.where((p) => p.name == '金星').toList();
      final marsList =
          _chartData.planets!.where((p) => p.name == '火星').toList();

      if (venusList.isNotEmpty) {
        final venus = venusList.first;
        keyElements.add('金星${venus.sign}座第${venus.house}宮');
      }
      if (marsList.isNotEmpty) {
        final mars = marsList.first;
        keyElements.add('火星${mars.sign}座第${mars.house}宮');
      }
    }

    return keyElements;
  }

  /// 生成情感關係的建議
  List<String> _generateEmotionalRecommendations() {
    return [
      '了解金星與火星的互動模式，平衡吸引力與行動力',
      '探索第五宮與第七宮的主題，理解愛情與婚姻的需求',
      '關注月亮相位，療癒早年的情感創傷',
      '運用合盤分析，增進與伴侶的理解',
    ];
  }

  /// 生成情感關係的摘要
  String _generateEmotionalSummary() {
    return '情感與親密關係分析幫助理解個人的感情模式、吸引力類型、以及在關係中的需求與挑戰，'
        '透過金星火星、關係宮位、月亮相位等分析，提供改善關係品質的指引。';
  }

  /// 提取職涯使命的關鍵要素
  List<String> _extractCareerKeyElements() {
    final keyElements = <String>[];

    if (_chartData.houses != null && _chartData.houses!.cusps.length > 9) {
      final mcSign = _getZodiacSign(_chartData.houses!.cusps[9]); // 第十宮宮頭
      keyElements.add('中天$mcSign座');
    }

    return keyElements;
  }

  /// 生成職涯使命的建議
  List<String> _generateCareerRecommendations() {
    return [
      '根據中天星座選擇適合的職業方向',
      '整合第二、六、十宮的配置，找到理想的工作模式',
      '跟隨北交點的指引，實現人生使命',
      '運用法達主星的週期，把握職涯發展的時機',
    ];
  }

  /// 生成職涯使命的摘要
  String _generateCareerSummary() {
    return '職涯方向與人生使命分析協助釐清適合的發展方向與階段任務，'
        '透過中天、職業宮位、北交點等要素，找到個人的天職與使命。';
  }

  /// 提取心理療癒的關鍵要素
  List<String> _extractHealingKeyElements() {
    return ['月亮相位', '冥王星位置', '凱龍星位置', '第十二宮配置'];
  }

  /// 生成心理療癒的建議
  List<String> _generateHealingRecommendations() {
    return [
      '關注月亮、冥王星、凱龍星的相位，了解深層的情感模式',
      '探索第四宮與第十二宮，療癒家庭與潛意識的創傷',
      '平衡海王星與土星的能量，建立健康的界線',
      '運用阿拉伯點與莉莉絲，整合內在的陰影面向',
    ];
  }

  /// 生成心理療癒的摘要
  String _generateHealingSummary() {
    return '心理療癒與潛意識模式分析用於深層覺察與創傷處理，'
        '透過分析療癒行星、潛意識宮位等，提供心理成長的方向。';
  }

  /// 根據經度獲取星座
  String _getZodiacSign(double longitude) {
    final signs = AstrologyConstants.ZODIAC_SIGNS;
    final signIndex = (longitude / 30).floor();
    return signs[signIndex % 12];
  }

  // 以下方法為佔位符，實際實現時需要添加具體邏輯
  Future<ThemeAnalysisSection> _analyzeVenusAndMars() async =>
      _createPlaceholderSection('金星與火星分析');

  Future<ThemeAnalysisSection> _analyzeRelationshipHouses() async =>
      _createPlaceholderSection('關係宮位分析');

  Future<ThemeAnalysisSection> _analyzeMoonAspects() async =>
      _createPlaceholderSection('月亮相位分析');

  Future<ThemeAnalysisSection> _analyzeSynastryElements() async =>
      _createPlaceholderSection('合盤要素分析');

  Future<ThemeAnalysisSection> _analyzeNodesAndChiron() async =>
      _createPlaceholderSection('交點與凱龍星分析');

  Future<ThemeAnalysisSection> _analyzeMidheaven() async =>
      _createPlaceholderSection('中天分析');

  Future<ThemeAnalysisSection> _analyzeCareerHouses() async =>
      _createPlaceholderSection('職業宮位分析');

  Future<ThemeAnalysisSection> _analyzeNodesAxis() async =>
      _createPlaceholderSection('南北交點軸分析');

  Future<ThemeAnalysisSection> _analyzeHouseRulerConnections() async =>
      _createPlaceholderSection('宮主星連結分析');

  Future<ThemeAnalysisSection> _analyzeCareerTiming() async =>
      _createPlaceholderSection('職涯時機分析');

  Future<ThemeAnalysisSection> _analyzeHealingPlanets() async =>
      _createPlaceholderSection('療癒行星分析');

  Future<ThemeAnalysisSection> _analyzeUnconsciousHouses() async =>
      _createPlaceholderSection('潛意識宮位分析');

  Future<ThemeAnalysisSection> _analyzeNeptuneSaturn() async =>
      _createPlaceholderSection('海王星土星分析');

  Future<ThemeAnalysisSection> _analyzeArabicPoints() async =>
      _createPlaceholderSection('阿拉伯點分析');

  Future<ThemeAnalysisSection> _analyzeLilith() async =>
      _createPlaceholderSection('莉莉絲分析');

  Future<ThemeAnalysisSection> _analyzeTransits() async =>
      _createPlaceholderSection('流年分析');

  Future<ThemeAnalysisSection> _analyzeProgressions() async =>
      _createPlaceholderSection('次限推運分析');

  Future<ThemeAnalysisSection> _analyzeFirdariaSystem() async =>
      _createPlaceholderSection('法達系統分析');

  Future<ThemeAnalysisSection> _analyzeSolarArc() async =>
      _createPlaceholderSection('太陽弧推運分析');

  Future<ThemeAnalysisSection> _analyzeCompositeTransits() async =>
      _createPlaceholderSection('合盤流年分析');

  Future<ThemeAnalysisSection> _analyzeElectionalAstrology() async =>
      _createPlaceholderSection('擇日占星分析');

  Future<ThemeAnalysisSection> _analyzeHouseTransits() async =>
      _createPlaceholderSection('宮位流年分析');

  Future<ThemeAnalysisSection> _analyzeTransitTriggers() async =>
      _createPlaceholderSection('流年觸發分析');

  Future<ThemeAnalysisSection> _analyzeFateVsFreeWill() async =>
      _createPlaceholderSection('宿命與自由意志分析');

  Future<ThemeAnalysisSection> _analyzeHoraryGuidance() async =>
      _createPlaceholderSection('卜卦指引分析');

  Future<ThemeAnalysisSection> _analyzeSpecificStyle(
          ConsultationStyle style) async =>
      _createPlaceholderSection('${style.name}風格分析');

  Future<ThemeAnalysisSection> _analyzeStyleComparison() async =>
      _createPlaceholderSection('風格比較分析');

  List<String> _extractTransitionalKeyElements() => ['流年重點', '推運要素', '法達週期'];

  List<String> _generateTransitionalRecommendations() => ['把握流年機會', '注意推運變化'];

  String _generateTransitionalSummary() => '流年與推運階段分析摘要';

  List<String> _extractDecisionKeyElements() => ['決策要素', '時機選擇'];

  List<String> _generateDecisionRecommendations() => ['善用擇日', '理性決策'];

  String _generateDecisionSummary() => '決策與問題討論分析摘要';

  List<String> _extractStyleKeyElements(ConsultationStyle? style) =>
      ['風格特色', '分析重點'];

  List<String> _generateStyleRecommendations(ConsultationStyle? style) =>
      ['選擇適合風格', '整合不同觀點'];

  String _generateStyleSummary(ConsultationStyle? style) => '占星諮詢風格差異分析摘要';

  /// 創建佔位符章節
  ThemeAnalysisSection _createPlaceholderSection(String title) {
    return ThemeAnalysisSection(
      title: title,
      content: '此部分分析正在開發中，將在未來版本中提供詳細內容。',
      relatedPlanets: [],
      relatedHouses: [],
      relatedAspects: [],
      importance: 2,
    );
  }
}
