import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../core/utils/web_fullscreen_helper.dart' if (dart.library.io) '../../../core/utils/web_fullscreen_helper_stub.dart';
import '../../../data/services/notification/notification_service.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/unified_card.dart';
import '../../viewmodels/recent_persons_viewmodel.dart';
import '../ai_interpretation_result_page.dart';
import '../analysis/current_events_analysis_page.dart';
import '../analysis/divination_analysis_page.dart';
import '../analysis/fortune_analysis_page.dart';
import '../analysis/personal_analysis_page.dart';
import '../analysis/starlight_relationship_analysis_page.dart';
import '../birth_data_form_page.dart';
import '../celebrity_examples_page.dart';
import '../chart_page.dart';
import '../chart_selection_page.dart';
import '../eclipse_setup_page.dart';
import '../equinox_solstice_page.dart';
import '../learning/aspects_knowledge_page.dart';
import '../learning/astrology_faq_page.dart';
import '../learning/basic_knowledge_page.dart';
import '../learning/houses_knowledge_page.dart';
import '../learning/planet_knowledge_page.dart';
import '../notification/notification_page.dart';
import '../onboarding/user_mode_selection_page.dart';
import '../person_selector_page.dart';
import '../starlight_chart_selection_page.dart';
import '../video_list_page.dart';

/// Starlight 初心者模式主頁
class StarlightHomePage extends StatefulWidget {
  const StarlightHomePage({super.key});

  @override
  State<StarlightHomePage> createState() => _StarlightHomePageState();
}

class _StarlightHomePageState extends State<StarlightHomePage> {

  @override
  void initState() {
    super.initState();

    // 設置全螢幕狀態變化回調
    if (kIsWeb) {
      WebFullscreenHelper.setOnFullscreenChanged(() {
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  @override
  void dispose() {
    // 移除全螢幕狀態變化回調
    if (kIsWeb) {
      WebFullscreenHelper.removeOnFullscreenChanged();
    }
    super.dispose();
  }

  /// 創建我的星盤
  Future<void> _createMyChart(HomeViewModel viewModel) async {
    if (viewModel.selectedPerson != null) {
      // 如果有選中的出生資料，直接使用
      _navigateToChart(viewModel.selectedPerson!);
    } else {
      // 如果沒有資料，導航到新增頁面
      final result = await Navigator.push<BirthData>(
        context,
        MaterialPageRoute(
          builder: (context) => const BirthDataFormPage(),
        ),
      );

      if (result != null && mounted) {
        try {
          // 使用 HomeViewModel 來添加和設定選中的人物
          viewModel.setSelectedPerson(result);

          // 導航到星盤頁面
          _navigateToChart(result);
        } catch (e) {
          // 如果保存失敗，顯示錯誤訊息但仍然導航到星盤頁面
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('保存出生資料失敗：$e'),
                backgroundColor: Colors.red,
              ),
            );
          }
          // 仍然導航到星盤頁面
          _navigateToChart(result);
        }
      }
    }
  }

  /// 導航到星盤頁面
  void _navigateToChart(BirthData birthData) {
    // 記錄選中的人物
    try {
      final recentPersonsViewModel =
          Provider.of<RecentPersonsViewModel>(context, listen: false);
      recentPersonsViewModel.recordSelectedPerson(birthData);
    } catch (e) {
      // 如果記錄失敗，不影響導航功能
    }

    // 創建 ChartData 對象
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) =>
              ChartViewModel.withChartData(initialChartData: chartData),
          child: ChartPage(chartData: chartData),
        ),
      ),
    );
  }

  /// 顯示人物選擇頁面
  Future<void> _showBirthDataSelection(HomeViewModel viewModel) async {
    if (viewModel.birthDataList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有可用的出生資料，請先新增出生資料')),
      );
      // 如果沒有資料，導航到新增頁面
      _createMyChart(viewModel);
      return;
    }

    final BirthData? result = await PersonSelectorPage.show(
      context: context,
      birthDataList: viewModel.birthDataList,
      title: '選擇出生資料',
      buttonColor: AppColors.solarAmber,
    );

    if (result != null) {
      viewModel.setSelectedPerson(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HomeViewModel(),
      child: Consumer<HomeViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            backgroundColor: AppColors.lightCornsilk,
            appBar: _buildModernAppBar(),
            body: SafeArea(
              child: RefreshIndicator(
                onRefresh: () async {
                  // 刷新客戶資料
                  await viewModel.loadBirthData();
                },
                child: ResponsivePageWrapper(
                  maxWidth: 900.0, // 初心者模式稍微小一些，更溫馨
                  child: ListView(
                    padding: ResponsiveUtils.getResponsivePadding(context),
                    children: [
                      // 歡迎卡片
                      ResponsiveCardWrapper(
                        maxWidth: 700.0,
                        child: _buildWelcomeCard(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 快速開始卡片
                      ResponsiveCardWrapper(
                        maxWidth: 700.0,
                        child: _buildQuickStartCard(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 我的本命分析區域
                      ResponsiveCardWrapper(
                        maxWidth: 700.0,
                        child: _buildMyAnalysisSection(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 我的運勢分析區域
                      ResponsiveCardWrapper(
                        maxWidth: 700.0,
                        child: _buildMyFortuneSection(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 我的關係分析區域
                      ResponsiveCardWrapper(
                        maxWidth: 700.0,
                        child: _buildMyRelationshipSection(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 時事分析區域
                      ResponsiveCardWrapper(
                        maxWidth: 700.0,
                        child: _buildCurrentEventsSection(),
                      ),

                      const SizedBox(height: 20),

                      // 學習區域
                      ResponsiveCardWrapper(
                        maxWidth: 700.0,
                        child: _buildLearningSection(),
                      ),

                      const SizedBox(height: 20),

                      // 探索功能
                      // _buildExploreSection(),
                      //
                      // const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 構建現代化 AppBar
  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      backgroundColor: AppColors.lightCornsilk,
      elevation: 0,
      title: const Text(
        'Starlight',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.solarAmber,
        ),
      ),
      actions: [
        // 全螢幕按鈕（僅在網頁平台顯示，但排除iOS設備）
        if (kIsWeb && !WebFullscreenHelper.isIOSDevice)
          IconButton(
            onPressed: () async {
              // 調試：打印點擊前的狀態
              WebFullscreenHelper.debugPrintState();

              if (WebFullscreenHelper.isFullscreenEnabled) {
                await WebFullscreenHelper.exitFullscreen();
              } else {
                await WebFullscreenHelper.enterFullscreen();
              }

              // 調試：打印點擊後的狀態
              WebFullscreenHelper.debugPrintState();

              // 強制更新 UI（備用機制）
              if (mounted) {
                setState(() {});
              }
            },
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                WebFullscreenHelper.isFullscreenEnabled
                    ? Icons.fullscreen_exit
                    : Icons.fullscreen,
                color: AppColors.solarAmber,
                size: 20,
              ),
            ),
            tooltip: WebFullscreenHelper.isFullscreenEnabled ? '退出全螢幕' : '進入全螢幕',
          ),
        // 通知按鈕
        FutureBuilder<int>(
          future: NotificationService.getUnreadNotificationCount(),
          builder: (context, snapshot) {
            final unreadCount = snapshot.data ?? 0;
            return IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationPage(),
                  ),
                );
              },
              icon: Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.solarAmber.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.notifications,
                      color: AppColors.solarAmber,
                      size: 20,
                    ),
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              tooltip: '通知中心',
            );
          },
        ),

        IconButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    const UserModeSelectionPage(isModeSwitch: true),
              ),
            );
          },
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.solarAmber.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.swap_horiz,
              color: AppColors.solarAmber,
              size: 20,
            ),
          ),
          tooltip: '切換模式',
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  /// 構建歡迎卡片
  Widget _buildWelcomeCard(HomeViewModel viewModel) {
    return UnifiedCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.wb_sunny,
                  color: AppColors.solarAmber,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '歡迎回來！',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '開始您的占星之旅',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (viewModel.selectedPerson != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.person,
                    color: AppColors.royalIndigo,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '當前選擇：${viewModel.selectedPerson!.name}',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.royalIndigo,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建快速開始卡片
  Widget _buildQuickStartCard(HomeViewModel viewModel) {
    return UnifiedCard(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _createMyChart(viewModel),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    viewModel.selectedPerson != null
                        ? Icons.auto_awesome
                        : Icons.add_circle_outline,
                    color: AppColors.royalIndigo,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        viewModel.selectedPerson != null ? '我的星盤' : '創建星盤',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        viewModel.selectedPerson != null
                            ? '查看 ${viewModel.selectedPerson!.name} 的星盤'
                            : '開始您的占星之旅',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (viewModel.birthDataList.length > 1) ...[
                  GestureDetector(
                    onTap: () => _showBirthDataSelection(viewModel),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.solarAmber.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.people_alt,
                        color: AppColors.solarAmber,
                        size: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建我的本命分析區域
  Widget _buildMyAnalysisSection(HomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '我的本命分析',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            if (viewModel.selectedPerson != null)
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PersonalAnalysisPage(
                        homeViewModel: viewModel,
                      ),
                    ),
                  );
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.royalIndigo.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '查看全部',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.royalIndigo,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppColors.royalIndigo,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (viewModel.selectedPerson == null) ...[
          // 沒有選中人物時的提示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.solarAmber.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.solarAmber.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.solarAmber,
                  size: 32,
                ),
                const SizedBox(height: 8),
                const Text(
                  '需要星盤資料才能進行分析',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '創建或選擇星盤後即可開始個人分析',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ] else ...[
          // 有選中人物時顯示分析類別
          Column(
            children: [
              // 個人分析卡片
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12, // 增加水平間距
                  mainAxisSpacing: 12, // 增加垂直間距
                  mainAxisExtent: 130, // 增加高度以適應優化後的卡片
                ),
                itemCount: 4,
                // 總共 4 個分析卡片
                itemBuilder: (context, index) {
                  final analysisItems = [
                    {
                      'title': '個性與人格',
                      'subtitle': '性格特質分析',
                      'icon': Icons.psychology,
                      'color': const Color(0xFF6366F1),
                      'key': 'personality',
                    },
                    {
                      'title': '感情與婚姻',
                      'subtitle': '愛情運勢探索',
                      'icon': Icons.favorite,
                      'color': const Color(0xFFEC4899),
                      'key': 'love_marriage',
                    },
                    {
                      'title': '財富與格局',
                      'subtitle': '財運分析',
                      'icon': Icons.account_balance_wallet,
                      'color': const Color(0xFFF59E0B),
                      'key': 'wealth_fortune',
                    },
                    {
                      'title': '職業與志向',
                      'subtitle': '事業發展',
                      'icon': Icons.work,
                      'color': const Color(0xFF059669),
                      'key': 'career_ambition',
                    },
                  ];

                  final item = analysisItems[index];
                  return _buildAnalysisCard(
                    title: item['title'] as String,
                    subtitle: item['subtitle'] as String,
                    icon: item['icon'] as IconData,
                    color: item['color'] as Color,
                    onTap: () => _navigateToAnalysisDetail(
                        item['title'] as String,
                        item['subtitle'] as String,
                        viewModel.selectedPerson!),
                  );
                },
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 構建我的運勢分析區域
  Widget _buildMyFortuneSection(HomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '我的運勢分析',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            if (viewModel.selectedPerson != null)
              GestureDetector(
                onTap: () => _showAllAnalysisOptions(viewModel.selectedPerson!),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.solarAmber.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '查看全部',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.solarAmber,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppColors.solarAmber,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (viewModel.selectedPerson == null) ...[
          // 沒有選中人物時的提示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: AppColors.royalIndigo,
                  size: 32,
                ),
                const SizedBox(height: 8),
                const Text(
                  '需要星盤資料才能分析運勢',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '創建或選擇星盤後即可查看個人運勢',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ] else ...[
          // 有選中人物時顯示智能運勢分析
          _buildIntelligentFortuneContent(viewModel),
        ],
      ],
    );
  }

  /// 構建智能運勢分析內容
  Widget _buildIntelligentFortuneContent(HomeViewModel viewModel) {
    return Column(
      children: [
        // 運勢分析卡片網格 - 優化布局以適應更長的文字內容
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12, // 增加水平間距
            mainAxisSpacing: 12, // 增加垂直間距
            mainAxisExtent: 130, // 增加高度以容納更多文字內容
          ),
          itemCount: 4, // 顯示前8個主要運勢分析
          itemBuilder: (context, index) {
            final fortuneItems = FortuneCategory.getHomePageItems();
            final item = fortuneItems[index];

            return _buildAnalysisCard(
              title: item.name,
              subtitle: item.description,
              icon: item.icon,
              color: item.color,
              onTap: () => _navigateToFortuneAnalysisDetail(
                item.chartType,
                item.question ?? item.description,
                item.description,
                viewModel.selectedPerson!,
              ),
            );
          },
        ),
      ],
    );
  }

  /// 構建學習區域
  Widget _buildLearningSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '學習占星',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // 學習卡片網格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12, // 增加水平間距
            mainAxisSpacing: 12, // 增加垂直間距
            mainAxisExtent: 130, // 增加高度以適應優化後的卡片
          ),
          itemCount: 4,
          itemBuilder: (context, index) {
            final learningItems = [
              {
                'title': '星座意義',
                'subtitle': '十二星座',
                'icon': Icons.school,
                'color': const Color(0xFF6366F1),
              },
              {
                'title': '行星意義',
                'subtitle': '了解行星',
                'icon': Icons.public,
                'color': const Color(0xFFEC4899),
              },
              {
                'title': '宮位意義',
                'subtitle': '十二宮位',
                'icon': Icons.home,
                'color': const Color(0xFFF59E0B),
              },
              {
                'title': '相位意義',
                'subtitle': '行星相位',
                'icon': Icons.timeline,
                'color': const Color(0xFF059669),
              },
            ];

            final item = learningItems[index];
            return _buildAnalysisCard(
              title: item['title'] as String,
              subtitle: item['subtitle'] as String,
              icon: item['icon'] as IconData,
              color: item['color'] as Color,
              onTap: () => _navigateToLearningDetail(
                item['title'] as String,
                index,
              ),
            );
          },
        ),

        const SizedBox(height: 16),

        // 影片教學卡片
        GestureDetector(
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const VideoListPage(),
            ),
          ),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.solarAmber.withValues(alpha: 0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.video_library,
                    color: AppColors.solarAmber,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '影片教學',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '觀看占星學習影片與應用教學',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
          ),
        ),

        // 占星常見問題卡片
        GestureDetector(
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AstrologyFaqPage(),
            ),
          ),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.help_outline,
                    color: AppColors.royalIndigo,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '占星常見問題',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '解答你對占星學的疑問',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),

        // 名人解讀範例卡片
        GestureDetector(
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CelebrityExamplesPage(),
            ),
          ),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.solarAmber.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.star,
                    color: AppColors.solarAmber,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '名人解讀範例',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '透過知名人物學習占星分析',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 獲取運勢評分顏色
  Color _getFortuneScoreColor(int score) {
    switch (score) {
      case 5:
        return const Color(0xFF4CAF50);
      case 4:
        return const Color(0xFF8BC34A);
      case 3:
        return const Color(0xFFFFC107);
      case 2:
        return const Color(0xFFFF9800);
      case 1:
        return const Color(0xFFF44336);
      default:
        return const Color(0xFF9E9E9E);
    }
  }

  /// 構建分析卡片
  Widget _buildAnalysisCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // 讓 Column 根據內容調整大小
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 18,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 12,
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 標題文字 - 確保不會溢出
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
                height: 1.2, // 設置行高
              ),
              maxLines: 2, // 標題最多顯示2行
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 6), // 增加間距
            // 副標題文字 - 優化顯示
            Flexible(
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: 11, // 稍微縮小字體
                  color: Colors.grey[600],
                  height: 1.3, // 設置行高
                ),
                maxLines: 3, // 增加到3行以容納更多內容
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到分析詳情
  void _navigateToAnalysisDetail(
      String title, String subtitle, BirthData birthData) {
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartData,
          interpretationTitle: title,
          subtitle: subtitle,
          suggestedQuestions: [
            // _buildPlanetInterpretationPrompt(),
          ],
          autoExecuteFirstQuestion: false,
        ),
      ),
    );
  }

  /// 導航到運勢分析詳情
  void _navigateToFortuneAnalysisDetail(ChartType chartType, String question,
      String description, BirthData birthData) {
    // 創建 ChartData 對象
    final chartData = ChartData(
      chartType: chartType,
      primaryPerson: birthData,
      specificDate: _getSpecificDateForChartType(chartType),
    );

    // 生成建議問題
    final suggestedQuestions =
        _getSuggestedQuestionsForChartType(chartType, birthData.name);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartData,
          interpretationTitle: question,
          subtitle: description,
          suggestedQuestions: suggestedQuestions,
          autoExecuteFirstQuestion: false,
        ),
      ),
    );
  }

  /// 獲取星盤類型對應的特定日期
  DateTime? _getSpecificDateForChartType(ChartType chartType) {
    switch (chartType) {
      case ChartType.transit:
        return DateTime.now(); // 流年盤使用當前時間
      case ChartType.solarReturn:
      case ChartType.lunarReturn:
        return DateTime.now(); // 返照盤使用當前時間計算
      case ChartType.firdaria:
        return DateTime.now(); // 法達盤使用當前時間
      case ChartType.secondaryProgression:
        return DateTime.now(); // 推運盤使用當前時間
      default:
        return null; // 其他類型不需要特定日期
    }
  }

  /// 獲取星盤類型的標題
  String _getChartTypeTitle(ChartType chartType) {
    switch (chartType) {
      case ChartType.transit:
        return '流年運勢分析';
      case ChartType.solarReturn:
        return '年度運勢分析';
      case ChartType.lunarReturn:
        return '月度運勢分析';
      case ChartType.firdaria:
        return '人生階段分析';
      case ChartType.secondaryProgression:
        return '心理發展分析';
      default:
        return chartType.description;
    }
  }

  /// 獲取星盤類型對應的建議問題
  List<String> _getSuggestedQuestionsForChartType(
      ChartType chartType, String name) {
    switch (chartType) {
      case ChartType.transit:
        return [
          '近期有哪些星象正在影響我？我該注意什麼？',
          '這陣子的運勢起伏重點是什麼？',
          '我可以怎麼掌握眼前的愛情、事業或財務機會？',
        ];
      case ChartType.solarReturn:
        return [
          '今年對我來說最重要的成長主題是什麼？',
          '生日年盤顯示哪些新機會或挑戰會出現？',
          '這一年在哪些領域特別適合展開新計畫？',
        ];
      case ChartType.lunarReturn:
        return [
          '這個月情緒或生活節奏會有什麼轉變？',
          '短期內我可以專注在哪些目標？',
          '本月是否適合處理感情、人際或財務問題？',
        ];
      case ChartType.firdaria:
        return [
          '我正在經歷哪個人生階段？這個階段的核心課題是什麼？',
          '目前主宰我運勢的行星帶來什麼學習與轉化？',
          '在這個大時期中，我應該培養什麼樣的心態或能力？',
        ];
      case ChartType.secondaryProgression:
        return [
          '我內在的成長節奏正在經歷哪些轉變？',
          '近期心理狀態起伏的源頭可能是什麼？',
          '我如何更好地理解並調適內在變化？',
        ];
      default:
        return [
          '請針對 $name 的 ${chartType.displayName} 進行解析。',
          '這個星盤揭示了哪些我現在最該關注的面向？',
          '我該如何把這份分析應用在當下生活中？',
        ];
    }
  }

  /// 顯示所有運勢分析選項
  void _showAllAnalysisOptions(BirthData birthData) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FortuneAnalysisPage(
          selectedPerson: birthData,
        ),
      ),
    );
  }

  /// 導航到學習詳情
  void _navigateToLearningDetail(String title, int index) {
    switch (index) {
      case 0:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const BasicKnowledgePage(),
          ),
        );
        break;
      case 1:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PlanetKnowledgePage(),
          ),
        );
        break;
      case 2:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const HousesKnowledgePage(),
          ),
        );
        break;
      case 3:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const AspectsKnowledgePage(),
          ),
        );
        break;
    }
  }

  /// 導航到星盤分析詳情
  void _navigateToChartAnalysisDetail(
    ChartType chartType,
    String title,
    BirthData birthData,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          primaryPerson: birthData,
          initialChartType: chartType,
          fromAnalysisPage: true,
        ),
      ),
    );
  }

  /// 構建我的關係分析區域
  Widget _buildMyRelationshipSection(HomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '我的關係分析',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            if (viewModel.selectedPerson != null)
              GestureDetector(
                onTap: () => _showAllRelationshipOptions(viewModel.selectedPerson!),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.cosmicPurple.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.cosmicPurple.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '查看全部',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.cosmicPurple,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppColors.cosmicPurple,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (viewModel.selectedPerson == null) ...[
          // 沒有選中人物時的提示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.cosmicPurple.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.cosmicPurple.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.favorite,
                  color: AppColors.cosmicPurple,
                  size: 32,
                ),
                const SizedBox(height: 8),
                const Text(
                  '需要星盤資料才能分析關係',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '創建或選擇星盤後即可開始關係分析',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ] else ...[
          // 有選中人物時顯示關係分析類別
          _buildRelationshipAnalysisContent(viewModel),
        ],
      ],
    );
  }

  /// 構建關係分析內容
  Widget _buildRelationshipAnalysisContent(HomeViewModel viewModel) {
    return Column(
      children: [
        // 關係分析卡片網格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            mainAxisExtent: 130,
          ),
          itemCount: 4,
          itemBuilder: (context, index) {
            final relationshipItems = _getRelationshipAnalysisItems();
            final item = relationshipItems[index];

            return _buildAnalysisCard(
              title: item['title'],
              subtitle: item['subtitle'],
              icon: item['icon'],
              color: item['color'],
              onTap: () => _navigateToRelationshipAnalysis(
                item['chartType'],
                item['title'],
                viewModel.selectedPerson!,
              ),
            );
          },
        ),
      ],
    );
  }

  /// 獲取關係分析項目
  List<Map<String, dynamic>> _getRelationshipAnalysisItems() {
    return [
      {
        'title': '緣分配對',
        'subtitle': '看看你們是否天生一對',
        'icon': Icons.people,
        'color': AppColors.cosmicPurple,
        'chartType': ChartType.synastry,
      },
      {
        'title': '愛情密碼',
        'subtitle': '探索你們的愛情潛力',
        'icon': Icons.favorite_border,
        'color': AppColors.starlightAccent,
        'chartType': ChartType.composite,
      },
      {
        'title': '相遇時機',
        'subtitle': '分析你們相遇的意義',
        'icon': Icons.timeline,
        'color': AppColors.solarAmber,
        'chartType': ChartType.davison,
      },
      {
        'title': '深層連結',
        'subtitle': '揭示內心的真實感受',
        'icon': Icons.psychology,
        'color': AppColors.royalIndigo,
        'chartType': ChartType.marks,
      },
    ];
  }



  /// 導航到關係分析
  void _navigateToRelationshipAnalysis(
    ChartType chartType,
    String title,
    BirthData primaryPerson,
  ) {
    // 使用現有的雙人分析處理機制
    _handleTwoPeopleChart(chartType, title, primaryPerson);
  }

  /// 處理雙人分析
  Future<void> _handleTwoPeopleChart(
    ChartType chartType,
    String title,
    BirthData primaryPerson,
  ) async {
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StarlightChartSelectionPage(
            primaryPerson: primaryPerson,
            chartType: chartType,
            analysisTitle: title,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 顯示所有關係分析選項
  void _showAllRelationshipOptions(BirthData selectedPerson) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StarlightRelationshipAnalysisPage(
          primaryPerson: selectedPerson,
        ),
      ),
    );
  }

  /// 構建時事分析區域 - 參考 StarmasterHomePage 的設計
  Widget _buildCurrentEventsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '時事分析',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 16),

        // 卜卦分析卡片 - 使用 UnifiedFeatureCard
        UnifiedFeatureCard(
          title: '問事占星',
          subtitle: '有疑問時，讓星星給你答案',
          icon: Icons.auto_awesome,
          color: const Color(0xFF9C27B0),
          onTap: _navigateToDivination,
        ),

        const SizedBox(height: 12),

        // 二分二至盤和日月蝕盤 - 使用 Row 布局
        Row(
          children: [
            Expanded(
              child: _buildCurrentEventsToolCard(
                title: '季節星盤',
                subtitle: '四季變化對你的影響',
                icon: Icons.wb_sunny,
                color: const Color(0xFF4CAF50),
                onTap: _navigateToEquinoxSolstice,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildCurrentEventsToolCard(
                title: '日月蝕星盤',
                subtitle: '重要天象帶來的轉機',
                icon: Icons.brightness_2,
                color: const Color(0xFFFF5722),
                onTap: _navigateToEclipseChart,
              ),
            ),
          ],
        ),
      ],
    );
  }
  /// 構建時事分析工具卡片 - 參考 StarmasterHomePage 的 _buildModernToolCard
  Widget _buildCurrentEventsToolCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return UnifiedCard(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }







  /// 導航到問事占星頁面
  void _navigateToDivination() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DivinationAnalysisPage(
          title: '問事占星',
          description: '有疑問時，讓星星給你答案',
        ),
      ),
    );
  }

  /// 導航到季節星盤頁面
  void _navigateToEquinoxSolstice() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EquinoxSolsticePage(),
      ),
    );
  }

  /// 導航到日月蝕星盤頁面
  void _navigateToEclipseChart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EclipseSetupPage(),
      ),
    );
  }

  /// 導航到時事分析頁面
  void _navigateToCurrentEventsAnalysis() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CurrentEventsAnalysisPage(),
      ),
    );
  }
}
